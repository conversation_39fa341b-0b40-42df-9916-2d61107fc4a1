#!/usr/bin/env python3
"""
测试AI推理交易默认配置
"""

import requests
from bs4 import BeautifulSoup
import re

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_default_trading_config():
    """测试默认交易配置"""
    
    print("⚙️ 测试AI推理交易默认配置")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            print(f"✅ 推理页面加载成功")
            
            # 测试配置项
            config_tests = [
                {
                    'name': '交易手数',
                    'id': 'tradingLotSize',
                    'expected_value': '0.01',
                    'description': '默认交易手数应为0.01'
                },
                {
                    'name': '最大持仓数',
                    'id': 'maxPositions', 
                    'expected_value': '3',
                    'description': '默认最大持仓数应为3'
                },
                {
                    'name': '止损点数',
                    'id': 'stopLossPips',
                    'expected_value': '50',
                    'description': '默认止损点数应为50'
                },
                {
                    'name': '止盈点数',
                    'id': 'takeProfitPips',
                    'expected_value': '100',
                    'description': '默认止盈点数应为100'
                },
                {
                    'name': '最低置信度',
                    'id': 'minConfidence',
                    'expected_value': '0.75',
                    'description': '默认最低置信度应为0.75'
                },
                {
                    'name': '推理间隔',
                    'id': 'inferenceInterval',
                    'expected_value': '60',
                    'description': '默认推理间隔应为60秒'
                },
                {
                    'name': '交易开始时间',
                    'id': 'tradingStartTime',
                    'expected_value': '00:05',
                    'description': '默认交易开始时间应为00:05'
                },
                {
                    'name': '交易结束时间',
                    'id': 'tradingEndTime',
                    'expected_value': '23:55',
                    'description': '默认交易结束时间应为23:55'
                }
            ]
            
            test_results = []
            
            print(f"\n🔍 检查默认配置值:")
            
            for config in config_tests:
                # 查找对应的输入元素
                element = soup.find('input', {'id': config['id']})
                
                if element:
                    actual_value = element.get('value', '')
                    expected_value = config['expected_value']
                    
                    if actual_value == expected_value:
                        print(f"   ✅ {config['name']}: {actual_value} (正确)")
                        test_results.append(True)
                    else:
                        print(f"   ❌ {config['name']}: {actual_value} (期望: {expected_value})")
                        test_results.append(False)
                else:
                    print(f"   ❌ {config['name']}: 元素未找到")
                    test_results.append(False)
            
            # 检查复选框默认状态
            checkbox_tests = [
                {
                    'name': '启用自动交易',
                    'id': 'enableAutoTrading',
                    'expected_checked': False,
                    'description': '自动交易默认应为关闭'
                },
                {
                    'name': '移动止损',
                    'id': 'enableTrailingStop',
                    'expected_checked': False,
                    'description': '移动止损默认应为关闭'
                },
                {
                    'name': '新闻过滤',
                    'id': 'enableNewsFilter',
                    'expected_checked': True,
                    'description': '新闻过滤默认应为开启'
                }
            ]
            
            print(f"\n🔍 检查复选框默认状态:")
            
            for checkbox in checkbox_tests:
                element = soup.find('input', {'id': checkbox['id']})
                
                if element:
                    is_checked = element.has_attr('checked')
                    expected_checked = checkbox['expected_checked']
                    
                    if is_checked == expected_checked:
                        status = "开启" if is_checked else "关闭"
                        print(f"   ✅ {checkbox['name']}: {status} (正确)")
                        test_results.append(True)
                    else:
                        actual_status = "开启" if is_checked else "关闭"
                        expected_status = "开启" if expected_checked else "关闭"
                        print(f"   ❌ {checkbox['name']}: {actual_status} (期望: {expected_status})")
                        test_results.append(False)
                else:
                    print(f"   ❌ {checkbox['name']}: 元素未找到")
                    test_results.append(False)
            
            # 统计结果
            passed_tests = sum(test_results)
            total_tests = len(test_results)
            
            print(f"\n📊 配置测试结果:")
            print(f"   通过测试: {passed_tests}/{total_tests}")
            print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
            
            return passed_tests == total_tests
            
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_input_constraints():
    """测试输入约束"""
    
    print(f"\n🔒 测试输入约束")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 测试数值输入的约束
            constraint_tests = [
                {
                    'name': '交易手数',
                    'id': 'tradingLotSize',
                    'expected_min': '0.01',
                    'expected_max': '10',
                    'expected_step': '0.01'
                },
                {
                    'name': '最大持仓数',
                    'id': 'maxPositions',
                    'expected_min': '1',
                    'expected_max': '10',
                    'expected_step': '1'
                },
                {
                    'name': '止损点数',
                    'id': 'stopLossPips',
                    'expected_min': '10',
                    'expected_max': '500',
                    'expected_step': '5'
                },
                {
                    'name': '止盈点数',
                    'id': 'takeProfitPips',
                    'expected_min': '10',
                    'expected_max': '1000',
                    'expected_step': '5'
                },
                {
                    'name': '最低置信度',
                    'id': 'minConfidence',
                    'expected_min': '0.5',
                    'expected_max': '0.99',
                    'expected_step': '0.05'
                },
                {
                    'name': '推理间隔',
                    'id': 'inferenceInterval',
                    'expected_min': '30',
                    'expected_max': '300',
                    'expected_step': '30'
                }
            ]
            
            constraint_results = []
            
            print(f"🔍 检查输入约束:")
            
            for test in constraint_tests:
                element = soup.find('input', {'id': test['id']})
                
                if element:
                    actual_min = element.get('min', '')
                    actual_max = element.get('max', '')
                    actual_step = element.get('step', '')
                    
                    min_ok = actual_min == test['expected_min']
                    max_ok = actual_max == test['expected_max']
                    step_ok = actual_step == test['expected_step']
                    
                    if min_ok and max_ok and step_ok:
                        print(f"   ✅ {test['name']}: 约束正确 (min={actual_min}, max={actual_max}, step={actual_step})")
                        constraint_results.append(True)
                    else:
                        print(f"   ❌ {test['name']}: 约束错误")
                        if not min_ok:
                            print(f"      最小值: {actual_min} (期望: {test['expected_min']})")
                        if not max_ok:
                            print(f"      最大值: {actual_max} (期望: {test['expected_max']})")
                        if not step_ok:
                            print(f"      步长: {actual_step} (期望: {test['expected_step']})")
                        constraint_results.append(False)
                else:
                    print(f"   ❌ {test['name']}: 元素未找到")
                    constraint_results.append(False)
            
            passed_constraints = sum(constraint_results)
            total_constraints = len(constraint_results)
            
            print(f"\n📊 约束测试结果:")
            print(f"   通过测试: {passed_constraints}/{total_constraints}")
            print(f"   成功率: {passed_constraints/total_constraints*100:.1f}%")
            
            return passed_constraints == total_constraints
            
        else:
            print(f"❌ 页面访问失败")
            return False
            
    except Exception as e:
        print(f"❌ 约束测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("⚙️ AI推理交易默认配置测试")
    print("=" * 80)
    
    print("📋 配置修改内容:")
    print("• 交易手数: 0.1 → 0.01 (更保守的默认手数)")
    print("• 交易开始时间: 09:00 → 00:05 (几乎全天交易)")
    print("• 交易结束时间: 17:00 → 23:55 (几乎全天交易)")
    print("• 其他配置保持不变")
    
    # 测试默认配置
    config_ok = test_default_trading_config()
    
    # 测试输入约束
    constraints_ok = test_input_constraints()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if config_ok and constraints_ok:
        print(f"🎉 AI推理交易默认配置修改成功!")
        print(f"✅ 默认配置值正确")
        print(f"✅ 输入约束正确")
        
        print(f"\n💡 新的默认配置:")
        print(f"• 交易手数: 0.01 (最小风险)")
        print(f"• 最大持仓数: 3")
        print(f"• 止损点数: 50")
        print(f"• 止盈点数: 100")
        print(f"• 最低置信度: 0.75")
        print(f"• 推理间隔: 60秒")
        print(f"• 交易时间: 00:05 - 23:55 (几乎24小时)")
        print(f"• 自动交易: 关闭 (需手动启用)")
        print(f"• 移动止损: 关闭")
        print(f"• 新闻过滤: 开启")
        
        print(f"\n🎯 配置优势:")
        print(f"• 最小手数降低交易风险")
        print(f"• 几乎全天交易时间覆盖所有市场机会")
        print(f"• 保守的风险管理设置")
        print(f"• 需要手动启用自动交易，避免意外执行")
        
    else:
        print(f"⚠️ 配置修改可能存在问题")
        print(f"默认配置: {'✅' if config_ok else '❌'}")
        print(f"输入约束: {'✅' if constraints_ok else '❌'}")
    
    print(f"\n🔧 使用建议")
    print("=" * 80)
    
    print(f"📊 风险管理:")
    print(f"• 0.01手数适合小资金账户和测试")
    print(f"• 可根据账户大小调整手数")
    print(f"• 建议先用模拟账户测试")
    
    print(f"\n⏰ 交易时间:")
    print(f"• 00:05-23:55覆盖主要交易时段")
    print(f"• 避开周末市场关闭时间")
    print(f"• 可根据策略调整具体时间段")
    
    print(f"\n🤖 自动交易:")
    print(f"• 默认关闭，需手动启用")
    print(f"• 启用前请确认所有参数设置")
    print(f"• 建议先观察推理结果再启用自动交易")

if __name__ == '__main__':
    main()
