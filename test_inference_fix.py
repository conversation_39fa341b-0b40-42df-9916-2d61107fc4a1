#!/usr/bin/env python3
"""
测试修复后的推理功能
"""

import requests
import json
import time

def test_inference_fix():
    """测试修复后的推理功能"""
    
    print("🔧 测试修复后的AI推理功能")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        if not result.get('success'):
            print("❌ 模型列表API返回失败")
            return False
        
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        print(f"总模型数: {len(models)}")
        print(f"完成训练的模型: {len(completed_models)}")
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        # 测试第一个模型的推理
        test_model = completed_models[0]
        model_id = test_model['id']
        
        print(f"\n🔍 测试模型推理: {test_model['name']} ({model_id[:8]}...)")
        print(f"模型信息: {test_model['symbol']} {test_model['timeframe']}")
        
        # 准备推理请求
        inference_data = {
            'model_id': model_id,
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True
        }
        
        print(f"\n🚀 执行推理测试...")
        
        start_time = time.time()
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=inference_data,
                               headers={'Content-Type': 'application/json'})
        end_time = time.time()
        
        print(f"推理耗时: {end_time - start_time:.2f}秒")
        
        if response.status_code != 200:
            print(f"❌ 推理请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
        
        result = response.json()
        
        if not result.get('success'):
            print(f"❌ 推理失败: {result.get('error')}")
            return False
        
        # 检查推理结果
        results = result.get('results', [])
        processing_time = result.get('processing_time', 0)
        
        print("✅ 推理成功完成!")
        print("=" * 50)
        print(f"📊 推理统计:")
        print(f"  处理时间: {processing_time:.3f}秒")
        print(f"  预测结果数: {len(results)}")
        
        if results:
            print(f"\n🎯 推理结果:")
            for i, pred in enumerate(results[:3], 1):  # 显示前3个结果
                analysis = pred.get('analysis', {})
                print(f"  {i}. 预测: {pred.get('prediction', 'N/A')}")
                print(f"     置信度: {pred.get('confidence', 0)*100:.1f}%")
                print(f"     当前价格: {pred.get('current_price', 0):.5f}")
                print(f"     目标价格: {pred.get('price_target', 0):.5f}")
                print(f"     价格变化: {analysis.get('price_change', 0):.2f}%")
                print(f"     波动性: {analysis.get('volatility', 0):.2f}%")
                print(f"     趋势: {analysis.get('trend', 'N/A')}")
                print(f"     信号强度: {analysis.get('signal_strength', 'N/A')}")
                print(f"     分析原因: {analysis.get('reason', 'N/A')}")
                if i < len(results):
                    print()
            
            if len(results) > 3:
                print(f"  ... 还有 {len(results) - 3} 个预测结果")
            
            # 验证结果格式
            first_result = results[0]
            analysis = first_result.get('analysis', {})
            
            # 检查是否包含波动性信息
            if 'volatility' in analysis:
                print(f"\n✅ 波动性计算正常: {analysis['volatility']:.2f}%")
            else:
                print(f"\n❌ 缺少波动性信息")
                return False
            
            # 检查其他必要字段
            required_fields = ['prediction', 'confidence', 'current_price', 'price_target']
            missing_fields = [field for field in required_fields if field not in first_result]
            
            if missing_fields:
                print(f"❌ 结果格式不完整，缺失字段: {missing_fields}")
                return False
            else:
                print(f"✅ 结果格式完整")
            
            return True
        else:
            print(f"\n⚠️ 推理成功但没有返回预测结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_backtest_fix():
    """测试修复后的回测功能"""
    
    print("\n🔵 测试修复后的回测功能")
    print("=" * 50)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        
        # 回测请求
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'start_date': '2024-07-22',
            'end_date': '2024-07-29',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.2
        }
        
        print(f"🔄 执行回测...")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=backtest_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                trades = result.get('trades', [])
                
                print(f"✅ 回测成功: {len(trades)} 笔交易")
                print(f"   总收益: {stats.get('total_return', 0):.2f}%")
                print(f"   胜率: {stats.get('win_rate', 0):.1f}%")
                
                return True
            else:
                print(f"❌ 回测失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 回测请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 回测测试异常: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 AI推理volatility变量修复测试")
    print("=" * 80)
    
    # 测试推理功能
    inference_success = test_inference_fix()
    
    # 测试回测功能
    backtest_success = test_backtest_fix()
    
    print(f"\n📊 测试结果")
    print("=" * 80)
    
    if inference_success and backtest_success:
        print("🎉 所有测试成功!")
        print("✅ volatility变量未定义问题已解决")
        print("✅ 智能推理功能正常工作")
        print("✅ 波动性计算正确")
        print("✅ 回测功能正常")
        
        print(f"\n💡 修复内容:")
        print("• 修复了volatility变量未定义的错误")
        print("• 改进了价格数据分析逻辑")
        print("• 增加了波动性计算功能")
        print("• 优化了数据不足时的处理")
        
    elif inference_success:
        print("🎉 推理测试成功!")
        print("✅ volatility变量问题已解决")
        print("⚠️ 回测功能可能需要进一步检查")
        
    else:
        print("❌ 测试失败")
        print("⚠️ volatility变量问题仍需解决")
        
        print(f"\n🔧 故障排除建议:")
        print("• 检查智能推理方法中的变量初始化")
        print("• 验证价格数据的有效性")
        print("• 确认numpy库正常工作")
        print("• 查看服务器详细日志")

if __name__ == '__main__':
    main()
