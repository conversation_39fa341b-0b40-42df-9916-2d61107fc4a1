#!/usr/bin/env python3
"""
测试早停机制修复
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_early_stopping_disabled():
    """测试禁用早停机制"""
    
    print("🚫 测试禁用早停机制")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    # 配置：禁用早停
    config = {
        'model_name': f'no_early_stop_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 10,  # 少量轮次
        'batch_size': 4,
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 3,
        'features': ['close', 'volume'],
        'early_stopping': False,  # 禁用早停
        'patience': 5,
        'min_epochs': 5
    }
    
    print(f"📝 测试配置 (早停: {config['early_stopping']}):")
    print(f"   轮次: {config['epochs']}")
    print(f"   早停启用: {config['early_stopping']}")
    
    return run_training_test(session, config, "禁用早停")

def test_early_stopping_enabled():
    """测试启用早停机制"""
    
    print(f"\n✅ 测试启用早停机制")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    # 配置：启用早停，但参数更宽松
    config = {
        'model_name': f'early_stop_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 50,  # 较多轮次
        'batch_size': 4,
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 3,
        'features': ['close', 'volume'],
        'early_stopping': True,  # 启用早停
        'patience': 20,  # 更宽松的耐心值
        'min_epochs': 10  # 最少训练轮次
    }
    
    print(f"📝 测试配置 (早停: {config['early_stopping']}):")
    print(f"   轮次: {config['epochs']}")
    print(f"   早停启用: {config['early_stopping']}")
    print(f"   耐心值: {config['patience']}")
    print(f"   最少轮次: {config['min_epochs']}")
    
    return run_training_test(session, config, "启用早停")

def run_training_test(session, config, test_name):
    """运行训练测试"""
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功: {task_id}")
                
                # 监控训练过程
                print(f"\n📊 监控训练过程:")
                
                completed_epochs = 0
                total_epochs = config['epochs']
                early_stopped = False
                final_status = None
                
                for i in range(120):  # 监控4分钟
                    time.sleep(2)
                    
                    try:
                        progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                        
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            
                            if progress_result.get('success'):
                                progress_data = progress_result['progress']
                                status = progress_data.get('status', 'unknown')
                                epoch = progress_data.get('epoch', 0)
                                train_loss = progress_data.get('train_loss')
                                val_loss = progress_data.get('val_loss')
                                
                                if epoch > completed_epochs:
                                    completed_epochs = epoch
                                    print(f"   轮次 {epoch}: 训练损失={train_loss:.4f}, 验证损失={val_loss:.4f}")
                                
                                if status == 'completed':
                                    final_status = 'completed'
                                    print(f"   🎉 训练完成!")
                                    
                                    # 判断是否早停
                                    if completed_epochs < total_epochs:
                                        early_stopped = True
                                        print(f"   🛑 早停触发: {completed_epochs}/{total_epochs} 轮")
                                    else:
                                        print(f"   ✅ 完整训练: {completed_epochs}/{total_epochs} 轮")
                                    break
                                elif status == 'failed':
                                    final_status = 'failed'
                                    print(f"   ❌ 训练失败")
                                    break
                                    
                            else:
                                print(f"   ❌ 获取进度失败: {progress_result.get('error')}")
                        else:
                            print(f"   ❌ 进度API错误: {progress_response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ 监控异常: {e}")
                
                # 分析结果
                print(f"\n📊 {test_name}测试结果:")
                print(f"   完成轮次: {completed_epochs}/{total_epochs}")
                print(f"   最终状态: {final_status}")
                print(f"   早停触发: {'是' if early_stopped else '否'}")
                
                # 验证结果
                if config['early_stopping']:
                    # 启用早停的情况
                    if completed_epochs >= config['min_epochs']:
                        print(f"✅ 早停机制正常：至少训练了{config['min_epochs']}轮")
                        return True
                    else:
                        print(f"❌ 早停过早触发：少于最少轮次{config['min_epochs']}")
                        return False
                else:
                    # 禁用早停的情况
                    if completed_epochs == total_epochs:
                        print(f"✅ 早停禁用正常：完成了所有{total_epochs}轮")
                        return True
                    else:
                        print(f"❌ 早停意外触发：应该完成{total_epochs}轮")
                        return False
                
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 早停机制修复验证")
    print("=" * 80)
    
    # 测试禁用早停
    no_early_stop_ok = test_early_stopping_disabled()
    
    # 测试启用早停
    early_stop_ok = test_early_stopping_enabled()
    
    print(f"\n📋 早停机制修复验证结果")
    print("=" * 80)
    
    if no_early_stop_ok and early_stop_ok:
        print(f"🎉 早停机制修复完全成功!")
        print(f"✅ 禁用早停功能正常")
        print(f"✅ 启用早停功能正常")
        
        print(f"\n💡 修复成果:")
        print(f"• 早停耐心值从10增加到20，减少过早停止")
        print(f"• 添加了最少训练轮次限制，防止过早触发")
        print(f"• 提供了早停开关，用户可以选择禁用")
        print(f"• 改进了早停日志，显示详细的触发原因")
        print(f"• 更新了前端配置，提供更好的用户控制")
        
    else:
        print(f"⚠️ 早停机制可能仍有问题")
        print(f"禁用早停: {'✅' if no_early_stop_ok else '❌'}")
        print(f"启用早停: {'✅' if early_stop_ok else '❌'}")
    
    print(f"\n🎯 用户使用建议")
    print("=" * 80)
    
    print(f"🔧 早停配置建议:")
    print(f"• 快速测试: 耐心值15, 最少轮次10")
    print(f"• 平衡训练: 耐心值20, 最少轮次20")
    print(f"• 高精度训练: 耐心值30, 最少轮次30")
    print(f"• 实验性训练: 耐心值40, 最少轮次50")
    
    print(f"\n💡 早停机制说明:")
    print(f"• 早停可以防止过拟合，提高模型泛化能力")
    print(f"• 但过于严格的早停可能导致训练不充分")
    print(f"• 建议根据数据量和模型复杂度调整参数")
    print(f"• 如果数据量大且模型复杂，可以禁用早停")

if __name__ == '__main__':
    main()
