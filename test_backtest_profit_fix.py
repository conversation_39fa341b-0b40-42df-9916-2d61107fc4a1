#!/usr/bin/env python3
"""
测试修复后的回测盈亏计算
"""

import requests
import json
import time

def test_backtest_profit_calculation():
    """测试回测盈亏计算修复"""
    
    print("🔧 测试修复后的AI推理回测盈亏计算")
    print("=" * 70)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        model_id = test_model['id']
        
        print(f"🔍 使用模型: {test_model['name']} ({model_id[:8]}...)")
        
        # 执行回测
        print(f"\n🔄 执行回测测试...")
        
        backtest_data = {
            'model_id': model_id,
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'start_date': '2025-07-25',
            'end_date': '2025-07-29',
            'initial_balance': 10000,
            'lot_size': 0.01,  # 默认手数0.01
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.1
        }
        
        print(f"回测配置:")
        print(f"  初始余额: ${backtest_data['initial_balance']}")
        print(f"  手数: {backtest_data['lot_size']}")
        print(f"  止损: {backtest_data['stop_loss_pips']} pips")
        print(f"  止盈: {backtest_data['take_profit_pips']} pips")
        print(f"  最小置信度: {backtest_data['min_confidence']}")
        
        start_time = time.time()
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=backtest_data)
        end_time = time.time()
        
        print(f"回测耗时: {end_time - start_time:.1f}秒")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                trades = result.get('trades', [])
                
                print(f"\n✅ 回测成功完成!")
                print(f"📊 回测统计:")
                print(f"  总交易: {stats.get('total_trades', 0)} 笔")
                print(f"  总收益: {stats.get('total_return', 0):.2f}%")
                print(f"  胜率: {stats.get('win_rate', 0):.1f}%")
                print(f"  盈利交易: {stats.get('winning_trades', 0)} 笔")
                print(f"  亏损交易: {stats.get('losing_trades', 0)} 笔")
                print(f"  最大回撤: {stats.get('max_drawdown', 0):.2f}%")
                
                if len(trades) > 0:
                    print(f"\n📈 交易记录分析 (前10笔):")
                    print(f"{'序号':<4} {'时间':<20} {'方向':<4} {'置信度':<8} {'手数':<6} {'入场价':<10} {'出场价':<10} {'盈亏':<10} {'点数':<8}")
                    print("-" * 90)
                    
                    for i, trade in enumerate(trades[:10], 1):
                        timestamp = trade['timestamp'][:19] if len(trade['timestamp']) > 19 else trade['timestamp']
                        prediction = trade['prediction']
                        confidence = f"{trade['confidence']*100:.1f}%"
                        lot_size = trade.get('lot_size', 0.01)
                        entry_price = f"{trade['entry_price']:.5f}"
                        exit_price = f"{trade['exit_price']:.5f}"
                        profit = f"${trade['profit']:.2f}"
                        pips = f"{trade['pips']:.1f}"
                        
                        print(f"{i:<4} {timestamp:<20} {prediction:<4} {confidence:<8} {lot_size:<6} {entry_price:<10} {exit_price:<10} {profit:<10} {pips:<8}")
                    
                    if len(trades) > 10:
                        print(f"... 还有 {len(trades) - 10} 笔交易")
                    
                    # 验证盈亏计算的合理性
                    print(f"\n🔍 盈亏计算验证:")
                    
                    # 检查前几笔交易的计算
                    for i, trade in enumerate(trades[:3], 1):
                        entry = trade['entry_price']
                        exit = trade['exit_price']
                        prediction = trade['prediction']
                        profit = trade['profit']
                        pips = trade['pips']
                        lot_size = trade.get('lot_size', 0.01)
                        
                        # 手动计算验证
                        if prediction == 'BUY':
                            expected_pips = (exit - entry) * 100  # XAUUSD 1点 = 0.01
                            expected_profit = expected_pips * lot_size * 0.01  # 黄金每点价值
                        else:  # SELL
                            expected_pips = (entry - exit) * 100
                            expected_profit = expected_pips * lot_size * 0.01
                        
                        print(f"  交易 {i}: {prediction} {entry:.5f} → {exit:.5f}")
                        print(f"    计算点数: {expected_pips:.1f} pips (实际: {pips:.1f})")
                        print(f"    计算盈亏: ${expected_profit:.2f} (实际: ${profit:.2f})")
                        
                        # 检查计算是否正确
                        if abs(expected_pips - pips) < 0.1 and abs(expected_profit - profit) < 0.01:
                            print(f"    ✅ 计算正确")
                        else:
                            print(f"    ❌ 计算错误!")
                            return False
                    
                    return True
                else:
                    print(f"\n⚠️ 没有产生交易")
                    return False
            else:
                print(f"❌ 回测失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 回测请求失败: HTTP {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_manual_calculation():
    """手动测试盈亏计算公式"""
    
    print(f"\n🧮 手动测试盈亏计算公式")
    print("=" * 50)
    
    # 测试案例
    test_cases = [
        {
            'name': 'SELL 3264.81 → 3263.55 (下跌1.26)',
            'prediction': 'SELL',
            'entry_price': 3264.81,
            'exit_price': 3263.55,
            'lot_size': 0.01,
            'expected_pips': 126,  # (3264.81 - 3263.55) * 100 = 126
            'expected_profit': 1.26  # 126 * 0.01 * 0.01 = 1.26
        },
        {
            'name': 'SELL 3263.55 → 3269.95 (上涨6.40)',
            'prediction': 'SELL',
            'entry_price': 3263.55,
            'exit_price': 3269.95,
            'lot_size': 0.01,
            'expected_pips': -640,  # (3263.55 - 3269.95) * 100 = -640
            'expected_profit': -6.40  # -640 * 0.01 * 0.01 = -6.40
        },
        {
            'name': 'BUY 3260.00 → 3265.00 (上涨5.00)',
            'prediction': 'BUY',
            'entry_price': 3260.00,
            'exit_price': 3265.00,
            'lot_size': 0.01,
            'expected_pips': 500,  # (3265.00 - 3260.00) * 100 = 500
            'expected_profit': 5.00  # 500 * 0.01 * 0.01 = 5.00
        }
    ]
    
    print("测试XAUUSD盈亏计算公式:")
    print("• 1点 = 0.01 (价格变化)")
    print("• 点数 = 价格差 × 100")
    print("• 盈亏 = 点数 × 手数 × 0.01")
    print()
    
    for case in test_cases:
        print(f"📊 {case['name']}")
        
        # 计算点数
        if case['prediction'] == 'BUY':
            pips = (case['exit_price'] - case['entry_price']) * 100
        else:  # SELL
            pips = (case['entry_price'] - case['exit_price']) * 100
        
        # 计算盈亏
        profit = pips * case['lot_size'] * 0.01
        
        print(f"  计算点数: {pips:.1f} pips (期望: {case['expected_pips']})")
        print(f"  计算盈亏: ${profit:.2f} (期望: ${case['expected_profit']:.2f})")
        
        if abs(pips - case['expected_pips']) < 0.1 and abs(profit - case['expected_profit']) < 0.01:
            print(f"  ✅ 公式正确")
        else:
            print(f"  ❌ 公式错误")
        print()

def main():
    """主函数"""
    
    print("🔧 AI推理回测盈亏计算修复测试")
    print("=" * 80)
    
    # 手动测试计算公式
    test_manual_calculation()
    
    # 测试实际回测
    success = test_backtest_profit_calculation()
    
    print(f"\n📊 测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 修复成功!")
        print("✅ 盈亏计算已修正")
        print("✅ 手数信息已添加")
        print("✅ 计算结果合理")
        
        print(f"\n💡 修复内容:")
        print("• 修正了点数计算：从 ×10000 改为 ×100")
        print("• 修正了每点价值：XAUUSD使用 ×0.01")
        print("• 添加了手数显示：默认0.01手")
        print("• 更新了前端表格：增加手数列")
        
        print(f"\n🎯 现在的计算逻辑:")
        print("• XAUUSD: 1点 = 0.01价格变化")
        print("• 点数 = 价格差 × 100")
        print("• 盈亏 = 点数 × 手数 × 0.01")
        print("• 0.01手每点价值 = $0.01")
        
    else:
        print("❌ 修复失败")
        print("⚠️ 盈亏计算仍有问题")
        
        print(f"\n🔧 故障排除建议:")
        print("• 检查点数计算公式")
        print("• 验证每点价值设置")
        print("• 确认手数传递正确")
        print("• 查看详细的计算日志")

if __name__ == '__main__':
    main()
