#!/usr/bin/env python3
"""
测试修复后的AI推理模型训练MT5连接
"""

import sys
import os
import time
import uuid
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mt5_connection_check():
    """测试MT5连接检查功能"""
    print("🔍 测试MT5连接检查功能")
    print("=" * 60)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        # 测试MT5连接检查
        print("🔄 执行MT5连接检查...")
        connection_ok = dl_service._ensure_mt5_connection_for_training()
        
        if connection_ok:
            print("✅ MT5连接检查通过")
            return True
        else:
            print("❌ MT5连接检查失败")
            return False
            
    except Exception as e:
        print(f"❌ MT5连接检查测试失败: {e}")
        return False

def test_training_data_preparation():
    """测试训练数据准备（修复后）"""
    print("\n📊 测试训练数据准备（修复后）")
    print("=" * 60)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        # 模拟训练配置（使用较小的数据集进行快速测试）
        config = {
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'days': 7  # 使用7天数据进行快速测试
            },
            'sequence_length': 20,  # 较短的序列长度
            'batch_size': 16
        }
        
        print(f"🔍 测试配置:")
        print(f"   品种: {config['symbol']}")
        print(f"   时间框架: {config['timeframe']}")
        print(f"   数据天数: {config['data_config']['days']}")
        print(f"   序列长度: {config['sequence_length']}")
        
        # 测试数据准备
        print("\n📈 开始数据准备...")
        start_time = time.time()
        
        X_train, X_val, y_train, y_val = dl_service._prepare_training_data(config)
        
        end_time = time.time()
        
        if X_train is not None and len(X_train) > 0:
            print(f"✅ 数据准备成功")
            print(f"   训练集形状: {X_train.shape}")
            print(f"   验证集形状: {X_val.shape}")
            print(f"   训练标签形状: {y_train.shape}")
            print(f"   验证标签形状: {y_val.shape}")
            print(f"   准备时间: {end_time - start_time:.2f}秒")
            return True
        else:
            print("❌ 数据准备失败")
            return False
            
    except Exception as e:
        print(f"❌ 训练数据准备测试失败: {e}")
        return False

def test_mock_training_start():
    """测试模拟训练启动（不实际训练）"""
    print("\n🚀 测试模拟训练启动")
    print("=" * 60)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        # 生成测试任务ID
        task_id = str(uuid.uuid4())
        model_id = f"test_model_{int(time.time())}"
        
        print(f"🆔 测试任务ID: {task_id}")
        print(f"🤖 测试模型ID: {model_id}")
        
        # 模拟训练配置
        config = {
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'days': 3  # 使用3天数据进行最快测试
            },
            'sequence_length': 10,  # 最短序列长度
            'batch_size': 8,
            'epochs': 1,  # 只训练1个epoch
            'learning_rate': 0.001
        }
        
        print(f"🔍 模拟训练配置:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        
        # 测试训练前的MT5连接检查
        print("\n🔄 测试训练前MT5连接检查...")
        connection_ok = dl_service._ensure_mt5_connection_for_training()
        
        if connection_ok:
            print("✅ 训练前MT5连接检查通过")
            
            # 测试数据准备阶段
            print("\n📊 测试数据准备阶段...")
            try:
                X_train, X_val, y_train, y_val = dl_service._prepare_training_data(config, task_id)
                
                if X_train is not None and len(X_train) > 0:
                    print("✅ 训练数据准备成功")
                    print("🎉 模拟训练启动测试通过")
                    return True
                else:
                    print("❌ 训练数据准备失败")
                    return False
                    
            except Exception as data_error:
                print(f"❌ 数据准备阶段失败: {data_error}")
                return False
        else:
            print("❌ 训练前MT5连接检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 模拟训练启动测试失败: {e}")
        return False

def test_api_training_request():
    """测试API训练请求"""
    print("\n🌐 测试API训练请求")
    print("=" * 60)
    
    try:
        import requests
        import json
        
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 准备训练请求数据
        training_data = {
            'model_name': f'测试模型_{int(time.time())}',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'days': 3  # 使用3天数据进行快速测试
            },
            'model_config': {
                'sequence_length': 10,
                'hidden_size': 32,
                'num_layers': 1,
                'dropout': 0.1
            },
            'training_config': {
                'epochs': 1,
                'batch_size': 8,
                'learning_rate': 0.001,
                'early_stopping_patience': 5
            }
        }
        
        print(f"📋 训练请求数据:")
        print(f"   模型名称: {training_data['model_name']}")
        print(f"   交易品种: {training_data['symbol']}")
        print(f"   时间框架: {training_data['timeframe']}")
        print(f"   数据天数: {training_data['data_config']['days']}")
        
        # 发送训练请求
        print("\n🚀 发送训练请求...")
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/train',
            json=training_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 训练请求提交成功")
                print(f"   任务ID: {result.get('task_id')}")
                print(f"   模型ID: {result.get('model_id')}")
                
                # 等待一段时间检查训练状态
                task_id = result.get('task_id')
                if task_id:
                    print(f"\n⏳ 等待5秒后检查训练状态...")
                    time.sleep(5)
                    
                    # 检查训练状态
                    status_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-status/{task_id}')
                    if status_response.status_code == 200:
                        status_result = status_response.json()
                        print(f"📊 训练状态: {status_result.get('status')}")
                        print(f"📈 训练进度: {status_result.get('progress', 0)}%")
                        
                        if status_result.get('status') == 'running':
                            print("✅ 训练正在进行中，MT5连接问题已修复")
                            return True
                        elif status_result.get('status') == 'failed':
                            error = status_result.get('error', '未知错误')
                            print(f"❌ 训练失败: {error}")
                            return False
                        else:
                            print(f"ℹ️ 训练状态: {status_result.get('status')}")
                            return True
                
                return True
            else:
                error = result.get('error', '未知错误')
                print(f"❌ 训练请求失败: {error}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            try:
                error_result = response.json()
                print(f"   错误信息: {error_result.get('error', '未知错误')}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API训练请求测试失败: {e}")
        return False

def main():
    print("🔧 AI推理模型训练MT5连接修复验证")
    print("=" * 80)
    print("🎯 验证目标: 确保模型训练时MT5连接正常，数据获取成功")
    print("=" * 80)
    
    # 测试1: MT5连接检查功能
    connection_check_ok = test_mt5_connection_check()
    
    # 测试2: 训练数据准备
    data_prep_ok = test_training_data_preparation()
    
    # 测试3: 模拟训练启动
    mock_training_ok = test_mock_training_start()
    
    # 测试4: API训练请求
    api_training_ok = test_api_training_request()
    
    print("\n" + "=" * 80)
    print("📋 修复验证结果总结")
    print("=" * 80)
    
    print(f"MT5连接检查: {'✅ 通过' if connection_check_ok else '❌ 失败'}")
    print(f"训练数据准备: {'✅ 通过' if data_prep_ok else '❌ 失败'}")
    print(f"模拟训练启动: {'✅ 通过' if mock_training_ok else '❌ 失败'}")
    print(f"API训练请求: {'✅ 通过' if api_training_ok else '❌ 失败'}")
    
    if all([connection_check_ok, data_prep_ok, mock_training_ok, api_training_ok]):
        print("\n🎉 MT5连接问题修复验证成功！")
        print("\n✅ 修复内容:")
        print("   • 训练前增加MT5连接状态检查")
        print("   • 数据获取失败时自动重连MT5")
        print("   • 增强的错误处理和重试机制")
        print("   • 详细的连接状态日志记录")
        
        print("\n🚀 现在可以正常进行AI推理模型训练:")
        print("   1. MT5连接会在训练前自动检查和修复")
        print("   2. 数据获取失败时会自动重试")
        print("   3. 提供详细的错误信息和解决建议")
        print("   4. 训练过程更加稳定可靠")
        
    else:
        print("\n⚠️ 部分验证失败，可能仍需进一步调试")
        print("\n💡 如果问题持续存在，请检查:")
        print("   • MT5终端是否正常运行")
        print("   • 交易账户是否已登录")
        print("   • 网络连接是否稳定")
        print("   • XAUUSD品种是否可用")

if __name__ == '__main__':
    main()
