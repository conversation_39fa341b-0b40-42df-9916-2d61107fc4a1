#!/usr/bin/env python3
"""
测试参数优化功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.deep_learning_service import DeepLearningService
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_parameter_optimization():
    """测试参数优化功能"""
    try:
        # 创建深度学习服务实例
        dl_service = DeepLearningService()
        
        # 测试参数
        model_id = "test_model"
        symbol = "XAUUSD"
        timeframe = "H1"
        optimization_period = "day"  # 使用较短的时间段进行测试
        
        logger.info("🔄 开始测试参数优化功能...")
        
        # 运行参数优化（这会测试我们修复的代码）
        result = dl_service.run_parameter_optimization(
            model_id=model_id,
            symbol=symbol,
            timeframe=timeframe,
            optimization_period=optimization_period
        )
        
        if result.get('success'):
            logger.info("✅ 参数优化测试成功！")
            logger.info(f"总组合数: {result.get('total_combinations', 0)}")
            logger.info(f"成功组合数: {result.get('successful_combinations', 0)}")
            
            # 显示最佳参数
            best_params = result.get('best_parameters')
            if best_params:
                logger.info("🏆 最佳参数组合:")
                for key, value in best_params.items():
                    logger.info(f"  {key}: {value}")
        else:
            logger.error(f"❌ 参数优化测试失败: {result.get('error', '未知错误')}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        return False

def test_backtest_with_trailing_stop():
    """测试带移动止损的回测功能"""
    try:
        # 创建深度学习服务实例
        dl_service = DeepLearningService()
        
        # 测试参数
        model_id = "test_model"
        symbol = "XAUUSD"
        timeframe = "H1"
        start_date = "2024-01-01"
        end_date = "2024-01-02"
        
        logger.info("🔄 开始测试带移动止损的回测功能...")
        
        # 运行回测（启用移动止损）
        result = dl_service.run_backtest(
            model_id=model_id,
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            initial_balance=10000,
            lot_size=0.01,
            stop_loss_pips=50,
            take_profit_pips=100,
            min_confidence=0.5,
            cliff_brake_enabled=False,
            trailing_stop_enabled=True,  # 启用移动止损
            trailing_stop_distance=20,
            trailing_stop_step=10
        )
        
        if result.get('success'):
            logger.info("✅ 带移动止损的回测测试成功！")
            stats = result.get('statistics', {})
            logger.info(f"总收益率: {stats.get('total_return', 0):.2f}%")
            logger.info(f"交易次数: {stats.get('total_trades', 0)}")
        else:
            logger.error(f"❌ 带移动止损的回测测试失败: {result.get('error', '未知错误')}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始测试修复后的代码...")
    
    # 测试1: 带移动止损的回测
    logger.info("\n" + "="*50)
    logger.info("测试1: 带移动止损的回测功能")
    logger.info("="*50)
    test1_result = test_backtest_with_trailing_stop()
    
    # 测试2: 参数优化（这个测试可能需要较长时间，所以放在最后）
    logger.info("\n" + "="*50)
    logger.info("测试2: 参数优化功能")
    logger.info("="*50)
    test2_result = test_parameter_optimization()
    
    # 总结
    logger.info("\n" + "="*50)
    logger.info("测试结果总结")
    logger.info("="*50)
    logger.info(f"带移动止损的回测: {'✅ 通过' if test1_result else '❌ 失败'}")
    logger.info(f"参数优化功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！修复成功！")
        sys.exit(0)
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
