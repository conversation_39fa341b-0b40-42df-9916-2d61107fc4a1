#!/usr/bin/env python3
"""
测试修复后的评分系统
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.deep_learning_service import DeepLearningService

def test_scoring_fix():
    """测试评分修复"""
    print("🔧 测试修复后的评分系统")
    print("=" * 60)
    
    dl_service = DeepLearningService()
    
    # 模拟两个回测结果
    high_return_result = {
        'statistics': {
            'total_return': 1.62,  # 1.62%收益率
            'win_rate': 50.0,      # 50%胜率
            'max_drawdown': -3.0,  # 3%最大回撤
            'sharpe_ratio': 1.2,   # 1.2夏普比率
            'total_trades': 7      # 7笔交易
        }
    }
    
    low_return_result = {
        'statistics': {
            'total_return': 0.86,  # 0.86%收益率
            'win_rate': 57.1,      # 57.1%胜率
            'max_drawdown': -1.0,  # 1%最大回撤
            'sharpe_ratio': 1.8,   # 1.8夏普比率
            'total_trades': 15     # 15笔交易
        }
    }
    
    # 计算评分
    high_score = dl_service._calculate_optimization_score(high_return_result, {})
    low_score = dl_service._calculate_optimization_score(low_return_result, {})
    
    print("📊 修复后的评分对比:")
    print(f"   高收益组合 (1.62%): {high_score:.2f} 分")
    print(f"   低收益组合 (0.86%): {low_score:.2f} 分")
    
    # 手动计算详细评分
    def calculate_detailed_score(stats, name):
        total_return = stats['total_return']
        win_rate = stats['win_rate']
        max_drawdown = abs(stats['max_drawdown'])
        sharpe_ratio = stats['sharpe_ratio']
        total_trades = stats['total_trades']
        
        print(f"\n🔢 {name} 详细评分:")
        
        # 收益率权重 60%
        return_score = min(total_return * 3, 60)
        print(f"   收益率得分: {return_score:.1f}/60 ({total_return:.2f}% * 3)")
        
        # 胜率权重 15%
        win_rate_score = (win_rate / 100) * 15
        print(f"   胜率得分: {win_rate_score:.1f}/15 ({win_rate:.1f}% / 100 * 15)")
        
        # 最大回撤权重 15%
        if max_drawdown > 0:
            drawdown_score = max(0, 15 - max_drawdown * 30)
        else:
            drawdown_score = 15
        print(f"   回撤得分: {drawdown_score:.1f}/15 (15 - {max_drawdown:.1f}% * 30)")
        
        # 夏普比率权重 7%
        sharpe_score = min(max(sharpe_ratio, 0) * 3.5, 7)
        print(f"   夏普得分: {sharpe_score:.1f}/7 ({sharpe_ratio:.1f} * 3.5)")
        
        # 交易次数权重 3%
        if total_trades >= 3 and total_trades <= 30:
            trade_score = 3
        elif total_trades > 0:
            trade_score = max(0, 3 - abs(total_trades - 15) * 0.1)
        else:
            trade_score = 0
        print(f"   交易得分: {trade_score:.1f}/3 ({total_trades}笔交易)")
        
        total_score = return_score + win_rate_score + drawdown_score + sharpe_score + trade_score
        print(f"   总评分: {total_score:.1f}")
        
        return total_score
    
    # 详细计算
    high_detailed = calculate_detailed_score(high_return_result['statistics'], "高收益组合")
    low_detailed = calculate_detailed_score(low_return_result['statistics'], "低收益组合")
    
    print(f"\n✅ 修复验证:")
    if high_score > low_score:
        print(f"   ✅ 修复成功！高收益组合评分更高")
        print(f"   📈 评分差距: {high_score - low_score:.2f} 分")
    else:
        print(f"   ❌ 仍需调整，低收益组合评分仍然更高")
        print(f"   📉 评分差距: {low_score - high_score:.2f} 分")
    
    return high_score > low_score

def test_various_scenarios():
    """测试各种场景下的评分"""
    print("\n🧪 测试各种场景下的评分")
    print("=" * 60)
    
    dl_service = DeepLearningService()
    
    scenarios = [
        {
            'name': '高收益高风险',
            'stats': {
                'total_return': 5.0,
                'win_rate': 45.0,
                'max_drawdown': -8.0,
                'sharpe_ratio': 0.8,
                'total_trades': 20
            }
        },
        {
            'name': '中等收益低风险',
            'stats': {
                'total_return': 2.0,
                'win_rate': 65.0,
                'max_drawdown': -2.0,
                'sharpe_ratio': 2.0,
                'total_trades': 12
            }
        },
        {
            'name': '低收益超低风险',
            'stats': {
                'total_return': 0.5,
                'win_rate': 80.0,
                'max_drawdown': -0.5,
                'sharpe_ratio': 3.0,
                'total_trades': 8
            }
        },
        {
            'name': '超高收益超高风险',
            'stats': {
                'total_return': 15.0,
                'win_rate': 30.0,
                'max_drawdown': -20.0,
                'sharpe_ratio': 0.5,
                'total_trades': 50
            }
        }
    ]
    
    print("📊 不同场景评分对比:")
    results = []
    
    for scenario in scenarios:
        score = dl_service._calculate_optimization_score({'statistics': scenario['stats']}, {})
        results.append((scenario['name'], scenario['stats']['total_return'], score))
        print(f"   {scenario['name']}: {score:.1f}分 (收益率{scenario['stats']['total_return']:.1f}%)")
    
    # 按评分排序
    results.sort(key=lambda x: x[2], reverse=True)
    
    print(f"\n🏆 评分排名:")
    for i, (name, return_rate, score) in enumerate(results, 1):
        print(f"   {i}. {name}: {score:.1f}分 (收益率{return_rate:.1f}%)")
    
    # 检查是否收益率高的排名靠前
    high_return_scenarios = [r for r in results if r[1] >= 2.0]
    if high_return_scenarios and high_return_scenarios[0] == results[0]:
        print(f"   ✅ 高收益场景排名靠前")
    else:
        print(f"   ⚠️ 评分系统可能仍需调整")

def main():
    print("🔧 评分系统修复验证")
    print("=" * 80)
    print("🎯 修复目标: 确保收益率高的组合获得更高评分")
    print("=" * 80)
    
    # 测试基本修复
    fix_success = test_scoring_fix()
    
    # 测试各种场景
    test_various_scenarios()
    
    print("\n" + "=" * 80)
    print("📋 修复总结")
    print("=" * 80)
    
    if fix_success:
        print("🎉 评分系统修复成功！")
        print("\n✅ 修复内容:")
        print("   • 收益率权重: 40% → 60% (大幅提高)")
        print("   • 胜率权重: 25% → 15% (适当降低)")
        print("   • 回撤权重: 20% → 15% (降低惩罚)")
        print("   • 夏普权重: 10% → 7% (适当降低)")
        print("   • 交易权重: 5% → 3% (进一步降低)")
        
        print("\n🎯 修复效果:")
        print("   • 高收益组合将获得更高评分")
        print("   • 收益率成为最重要的评分因素")
        print("   • 风险控制仍然重要但不会过度影响排名")
        print("   • 交易频率要求更加宽松")
        
        print("\n🚀 现在参数优化将正确地:")
        print("   • 优先推荐高收益的参数组合")
        print("   • 在收益率相近时考虑风险控制")
        print("   • 提供更合理的参数排名")
    else:
        print("⚠️ 评分系统仍需进一步调整")
        print("💡 建议进一步提高收益率权重或调整其他指标权重")

if __name__ == '__main__':
    main()
