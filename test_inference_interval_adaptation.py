#!/usr/bin/env python3
"""
测试推理间隔智能适配功能
"""

import requests
from bs4 import BeautifulSoup

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_inference_interval_ui():
    """测试推理间隔UI改进"""
    
    print("🔄 测试推理间隔UI改进")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            print(f"✅ 推理页面加载成功")
            
            # 检查推理间隔选择器
            interval_select = soup.find('select', {'id': 'inferenceInterval'})
            
            if interval_select:
                print(f"✅ 找到推理间隔选择器")
                
                # 检查选项
                options = interval_select.find_all('option')
                expected_options = [
                    ('auto', '自动适配 (推荐)'),
                    ('30', '30秒 (高频)'),
                    ('60', '1分钟'),
                    ('300', '5分钟'),
                    ('900', '15分钟'),
                    ('1800', '30分钟'),
                    ('3600', '1小时')
                ]
                
                print(f"🔍 检查推理间隔选项:")
                
                found_options = []
                for option in options:
                    value = option.get('value', '')
                    text = option.get_text().strip()
                    found_options.append((value, text))
                    print(f"   找到选项: {value} = {text}")
                
                # 验证选项完整性
                missing_options = []
                for expected_value, expected_text in expected_options:
                    found = any(value == expected_value and expected_text in text 
                              for value, text in found_options)
                    if found:
                        print(f"   ✅ {expected_value}: 存在")
                    else:
                        print(f"   ❌ {expected_value}: 缺失")
                        missing_options.append(expected_value)
                
                # 检查帮助文本
                help_text = soup.find('div', {'id': 'inferenceIntervalHelp'})
                if help_text:
                    print(f"✅ 找到帮助文本区域")
                    print(f"   内容: {help_text.get_text().strip()}")
                else:
                    print(f"❌ 未找到帮助文本区域")
                
                return len(missing_options) == 0
                
            else:
                print(f"❌ 未找到推理间隔选择器")
                return False
                
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_interval_mapping_logic():
    """测试间隔映射逻辑"""
    
    print(f"\n⚙️ 测试间隔映射逻辑")
    print("=" * 60)
    
    # 预期的时间框架到推理间隔的映射
    expected_mappings = {
        '1m': (30, '30秒'),      # 1分钟K线 → 30秒推理
        '5m': (150, '2分30秒'),   # 5分钟K线 → 2.5分钟推理
        '15m': (450, '7分30秒'),  # 15分钟K线 → 7.5分钟推理
        '30m': (900, '15分钟'),   # 30分钟K线 → 15分钟推理
        '1h': (1800, '30分钟'),   # 1小时K线 → 30分钟推理
        '4h': (7200, '2小时'),    # 4小时K线 → 2小时推理
        '1d': (21600, '6小时')    # 1天K线 → 6小时推理
    }
    
    print(f"📊 推荐的时间框架映射:")
    
    all_mappings_logical = True
    
    for timeframe, (interval_seconds, interval_display) in expected_mappings.items():
        # 计算时间框架的秒数
        if timeframe == '1m':
            tf_seconds = 60
        elif timeframe == '5m':
            tf_seconds = 300
        elif timeframe == '15m':
            tf_seconds = 900
        elif timeframe == '30m':
            tf_seconds = 1800
        elif timeframe == '1h':
            tf_seconds = 3600
        elif timeframe == '4h':
            tf_seconds = 14400
        elif timeframe == '1d':
            tf_seconds = 86400
        else:
            tf_seconds = 0
        
        # 计算推理间隔与时间框架的比例
        if tf_seconds > 0:
            ratio = interval_seconds / tf_seconds
            ratio_percent = ratio * 100
            
            print(f"   {timeframe:>3} → {interval_display:>8} (比例: {ratio_percent:5.1f}%)")
            
            # 检查比例是否合理 (推荐在10%-60%之间)
            if 0.1 <= ratio <= 0.6:
                print(f"       ✅ 比例合理")
            else:
                print(f"       ⚠️ 比例可能需要调整")
                all_mappings_logical = False
        else:
            print(f"   {timeframe:>3} → {interval_display:>8} (无法计算比例)")
    
    print(f"\n💡 设计原理:")
    print(f"• 推理间隔应该小于时间框架，但不能太频繁")
    print(f"• 推荐比例: 推理间隔 = 时间框架 × 25%-50%")
    print(f"• 这样既能及时捕捉变化，又不会过度消耗资源")
    
    return all_mappings_logical

def test_practical_scenarios():
    """测试实际使用场景"""
    
    print(f"\n🎯 测试实际使用场景")
    print("=" * 60)
    
    scenarios = [
        {
            'name': '高频交易策略',
            'timeframe': '1m',
            'expected_interval': 30,
            'reasoning': '1分钟K线需要快速响应，30秒推理合适'
        },
        {
            'name': '短线交易策略', 
            'timeframe': '5m',
            'expected_interval': 150,
            'reasoning': '5分钟K线，2.5分钟推理平衡效率和资源'
        },
        {
            'name': '中线交易策略',
            'timeframe': '1h',
            'expected_interval': 1800,
            'reasoning': '1小时K线，30分钟推理避免过度交易'
        },
        {
            'name': '长线交易策略',
            'timeframe': '1d',
            'expected_interval': 21600,
            'reasoning': '日线级别，6小时推理足够捕捉趋势变化'
        }
    ]
    
    print(f"📋 实际场景分析:")
    
    for scenario in scenarios:
        print(f"\n🔸 {scenario['name']}:")
        print(f"   时间框架: {scenario['timeframe']}")
        print(f"   推荐间隔: {scenario['expected_interval']}秒")
        print(f"   设计理由: {scenario['reasoning']}")
        
        # 计算每天的推理次数
        daily_inferences = 86400 // scenario['expected_interval']
        print(f"   每日推理次数: ~{daily_inferences}次")
        
        # 评估资源消耗
        if daily_inferences > 1000:
            print(f"   资源消耗: 🔴 高 (需要强大的服务器)")
        elif daily_inferences > 100:
            print(f"   资源消耗: 🟡 中等 (适中的服务器配置)")
        else:
            print(f"   资源消耗: 🟢 低 (普通服务器即可)")
    
    return True

def main():
    """主函数"""
    
    print("🔄 推理间隔智能适配功能测试")
    print("=" * 80)
    
    print("📋 功能改进:")
    print("• 推理间隔从固定输入改为智能选择器")
    print("• 新增'自动适配'选项，根据模型时间框架智能设置")
    print("• 提供多种预设间隔选项")
    print("• 动态显示推荐间隔和说明")
    
    print("\n💡 设计理念:")
    print("• 推理间隔应该与模型训练的时间框架相匹配")
    print("• 过于频繁的推理浪费资源且可能产生噪音")
    print("• 过于稀疏的推理可能错过重要的市场变化")
    print("• 智能适配在效率和准确性之间找到平衡")
    
    # 测试UI改进
    ui_ok = test_inference_interval_ui()
    
    # 测试映射逻辑
    logic_ok = test_interval_mapping_logic()
    
    # 测试实际场景
    scenarios_ok = test_practical_scenarios()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if ui_ok and logic_ok and scenarios_ok:
        print(f"🎉 推理间隔智能适配功能完全成功!")
        print(f"✅ UI界面改进正确")
        print(f"✅ 映射逻辑合理")
        print(f"✅ 实际场景适用")
        
        print(f"\n💡 功能亮点:")
        print(f"• 智能适配: 根据模型时间框架自动推荐最佳间隔")
        print(f"• 灵活选择: 用户可以选择自动适配或手动设置")
        print(f"• 资源优化: 避免过度频繁的推理，节省计算资源")
        print(f"• 策略匹配: 不同交易策略有对应的推理频率")
        
        print(f"\n🎯 使用建议:")
        print(f"• 新手用户: 选择'自动适配'，系统会智能设置")
        print(f"• 高频策略: 可选择30秒或1分钟间隔")
        print(f"• 长线策略: 可选择30分钟或1小时间隔")
        print(f"• 资源有限: 选择较长间隔减少计算负担")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步完善")
        print(f"UI界面: {'✅' if ui_ok else '❌'}")
        print(f"映射逻辑: {'✅' if logic_ok else '❌'}")
        print(f"实际场景: {'✅' if scenarios_ok else '❌'}")
    
    print(f"\n📊 时间框架与推理间隔对应表")
    print("=" * 80)
    
    print(f"{'时间框架':<8} {'推荐间隔':<12} {'每日推理次数':<12} {'适用策略'}")
    print(f"{'-'*8} {'-'*12} {'-'*12} {'-'*20}")
    print(f"{'1分钟':<8} {'30秒':<12} {'~2880次':<12} {'超高频交易'}")
    print(f"{'5分钟':<8} {'2分30秒':<12} {'~576次':<12} {'高频交易'}")
    print(f"{'15分钟':<8} {'7分30秒':<12} {'~192次':<12} {'短线交易'}")
    print(f"{'30分钟':<8} {'15分钟':<12} {'~96次':<12} {'短中线交易'}")
    print(f"{'1小时':<8} {'30分钟':<12} {'~48次':<12} {'中线交易'}")
    print(f"{'4小时':<8} {'2小时':<12} {'~12次':<12} {'中长线交易'}")
    print(f"{'1天':<8} {'6小时':<12} {'~4次':<12} {'长线交易'}")

if __name__ == '__main__':
    main()
