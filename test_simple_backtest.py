#!/usr/bin/env python3
"""
简单测试回测API
"""

import requests
import json

def test_simple_backtest():
    """简单测试回测API"""
    
    print("🔧 简单测试回测API")
    print("=" * 50)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        print(f"✅ 使用模型: {test_model['name']}")
        
        # 简单回测请求
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'start_date': '2025-07-28',
            'end_date': '2025-07-29',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.1
        }
        
        print(f"📤 发送回测请求...")
        print(f"请求数据: {json.dumps(backtest_data, indent=2, ensure_ascii=False)}")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=backtest_data,
                               headers={'Content-Type': 'application/json'})
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ JSON解析成功")
                print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('success'):
                    trades = result.get('trades', [])
                    stats = result.get('statistics', {})
                    
                    print(f"\n📊 回测结果:")
                    print(f"  交易数量: {len(trades)} 笔")
                    print(f"  总收益: {stats.get('total_return', 0):.2f}%")
                    print(f"  胜率: {stats.get('win_rate', 0):.1f}%")
                    
                    return True
                else:
                    print(f"❌ 回测失败: {result.get('error')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:500]}")
                return False
        else:
            print(f"❌ HTTP请求失败")
            print(f"响应内容: {response.text[:500]}")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    
    print("🔧 简单回测API测试")
    print("=" * 60)
    
    success = test_simple_backtest()
    
    if success:
        print("\n🎉 API测试成功!")
        print("✅ 回测API正常工作")
        print("✅ 可以进行前端界面测试")
    else:
        print("\n❌ API测试失败")
        print("⚠️ 需要检查后端问题")

if __name__ == '__main__':
    main()
