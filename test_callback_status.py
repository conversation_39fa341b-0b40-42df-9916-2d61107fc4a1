#!/usr/bin/env python3
"""
测试回调交易状态持久化功能
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print("❌ 登录失败")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_callback_status_api():
    """测试回调交易状态API"""
    
    print("🧪 测试回调交易状态API")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return
    
    try:
        # 测试状态API
        print("🔍 获取当前状态...")
        
        response = session.get('http://127.0.0.1:5000/api/callback-trading/status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                status = result.get('status', {})
                monitoring = result.get('monitoring', {})
                
                print("✅ 状态API响应正常")
                print(f"📊 状态详情:")
                print(f"   运行状态: {'运行中' if status.get('running') else '已停止'}")
                print(f"   运行时间: {status.get('running_time', '--')}")
                print(f"   今日交易: {status.get('today_trades', 0)} 次")
                print(f"   监控品种: {status.get('symbol', '--')}")
                print(f"   时间框架: {status.get('timeframe', '--')}")
                
                if monitoring:
                    print(f"   当前价格: {monitoring.get('current_price', '--')}")
                    print(f"   趋势方向: {monitoring.get('trend', '--')}")
                    print(f"   回调状态: {monitoring.get('callback_status', '--')}")
                
                return status.get('running', False)
            else:
                print(f"❌ 状态API错误: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态API测试失败: {e}")
        return False

def test_start_stop_trading():
    """测试启动和停止交易"""
    
    print("\n🔄 测试启动/停止交易功能")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return
    
    try:
        # 测试启动交易
        print("🚀 测试启动回调交易...")
        
        start_config = {
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'min_lot_size': 0.01,
            'max_lot_size': 0.1,
            'trend_period': 20,
            'callback_percent': 38.2,
            'stop_loss_percent': 2.0,
            'take_profit_percent': 4.0,
            'daily_trade_limit': 5
        }
        
        response = session.post(
            'http://127.0.0.1:5000/api/callback-trading/start',
            json=start_config,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 启动成功")
                
                # 等待一下，然后检查状态
                time.sleep(2)
                
                # 检查状态
                print("🔍 检查启动后状态...")
                is_running = test_callback_status_api()
                
                if is_running:
                    print("✅ 状态持久化正常")
                    
                    # 测试停止
                    print("\n🛑 测试停止交易...")
                    
                    stop_response = session.post('http://127.0.0.1:5000/api/callback-trading/stop')
                    
                    if stop_response.status_code == 200:
                        stop_result = stop_response.json()
                        
                        if stop_result.get('success'):
                            print("✅ 停止成功")
                            
                            # 再次检查状态
                            time.sleep(1)
                            print("🔍 检查停止后状态...")
                            is_running_after_stop = test_callback_status_api()
                            
                            if not is_running_after_stop:
                                print("✅ 停止状态持久化正常")
                            else:
                                print("❌ 停止状态持久化失败")
                        else:
                            print(f"❌ 停止失败: {stop_result.get('error')}")
                    else:
                        print(f"❌ 停止请求失败: {stop_response.status_code}")
                else:
                    print("❌ 启动状态持久化失败")
            else:
                print(f"❌ 启动失败: {result.get('error')}")
        else:
            print(f"❌ 启动请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 启动/停止测试失败: {e}")

def test_backtest_with_new_defaults():
    """测试新的默认参数回测"""
    
    print("\n🧪 测试新默认参数的回测")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return
    
    try:
        from datetime import datetime, timedelta
        
        # 使用新的默认参数
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)  # 最近7天
        
        config = {
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            # 新的默认参数
            'initial_capital': 10000,
            'lot_size': 0.01,  # 新默认值
            'risk_percent': 2.0,
            'max_positions': 4,  # 新默认值
            # 策略参数
            'trend_period': 20,
            'callback_percent': 38.2,
            'stop_loss_percent': 2.0,
            'take_profit_percent': 4.0
        }
        
        print(f"📊 回测配置（新默认参数）:")
        print(f"   每笔手数: {config['lot_size']} (新默认值)")
        print(f"   最大持仓: {config['max_positions']} (新默认值)")
        print(f"   趋势周期: {config['trend_period']} (H1框架下 = 20小时)")
        
        response = session.post(
            'http://127.0.0.1:5000/api/callback-trading/backtest',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                results = result['results']
                print("✅ 回测成功完成")
                print(f"📈 回测结果:")
                print(f"   总交易次数: {results['total_trades']}")
                print(f"   盈利次数: {results['winning_trades']}")
                print(f"   亏损次数: {results['losing_trades']}")
                print(f"   胜率: {results['win_rate']:.1f}%")
                print(f"   净盈亏: ${results['total_profit']:.2f}")
                print(f"   数据点数: {results['data_points']}")
            else:
                print(f"❌ 回测失败: {result.get('error')}")
        else:
            print(f"❌ 回测请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 回测测试失败: {e}")

def main():
    """主函数"""
    
    print("🧪 回调交易功能完善测试")
    print("=" * 60)
    
    # 测试状态API
    test_callback_status_api()
    
    # 测试启动停止功能
    test_start_stop_trading()
    
    # 测试新默认参数的回测
    test_backtest_with_new_defaults()
    
    print(f"\n🎉 测试完成!")
    print("=" * 60)
    
    print(f"\n📋 功能完善总结:")
    print(f"1. ✅ 回测默认参数已修改：手数0.01，最大持仓4")
    print(f"2. ✅ 添加了交易状态持久化功能")
    print(f"3. ✅ 添加了趋势判断周期的详细说明")
    print(f"4. ✅ 页面刷新后可自动恢复交易状态")

if __name__ == '__main__':
    main()
