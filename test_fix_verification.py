#!/usr/bin/env python3
"""
验证LSTMModel错误修复的测试脚本
"""

import sys
import os
import torch
import torch.nn as nn

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_lstm_model_fix():
    """测试LSTMModel修复"""
    print("🔧 测试LSTMModel错误修复")
    print("=" * 50)
    
    try:
        # 导入深度学习服务
        from services.deep_learning_service import DeepLearningService, LSTMModel
        
        print("✅ 成功导入DeepLearningService和LSTMModel")
        
        # 创建服务实例
        service = DeepLearningService()
        print("✅ 成功创建DeepLearningService实例")
        
        # 创建一个简单的LSTM模型
        model = LSTMModel(
            input_size=8,
            hidden_size=64,
            num_layers=2,
            output_size=1,
            dropout=0.2
        )
        print("✅ 成功创建LSTMModel实例")
        
        # 验证模型是PyTorch模型，不是字典
        print(f"📊 模型类型: {type(model)}")
        print(f"📊 是否为nn.Module: {isinstance(model, nn.Module)}")
        print(f"📊 是否为字典: {isinstance(model, dict)}")
        
        # 验证模型没有get方法（作为字典方法）
        has_get_method = hasattr(model, 'get')
        print(f"📊 模型是否有get方法: {has_get_method}")
        
        if has_get_method:
            print("⚠️ 注意：模型有get方法，但这不是字典的get方法")
        
        # 模拟修复前的错误情况
        print("\n🔍 模拟修复前的错误:")
        try:
            # 这应该会失败，因为LSTMModel没有get方法（作为字典）
            symbol = model.get('symbol', 'XAUUSD')
            print(f"❌ 意外成功：{symbol}")
        except AttributeError as e:
            print(f"✅ 预期的错误：{e}")
            print("   这证实了原始问题：LSTMModel对象没有'get'属性")
        
        # 模拟修复后的正确情况
        print("\n🔧 模拟修复后的正确做法:")
        model_info = {
            'symbol': 'XAUUSD',
            'timeframe': '1h',
            'status': 'completed',
            'config': {
                'model_type': 'lstm',
                'hidden_size': 64,
                'num_layers': 2
            }
        }
        
        # 这应该成功
        symbol = model_info.get('symbol', 'XAUUSD')
        print(f"✅ 修复后成功：symbol = {symbol}")
        
        print("\n🎯 修复验证结果:")
        print("✅ 确认LSTMModel是PyTorch模型，不是字典")
        print("✅ 确认原始错误：model.get()会失败")
        print("✅ 确认修复方案：使用model_info.get()成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        import traceback
        print(f"详细错误：{traceback.format_exc()}")
        return False

def test_code_fix_directly():
    """直接测试代码修复"""
    print("\n🔧 直接验证代码修复")
    print("=" * 50)
    
    try:
        # 读取修复后的代码
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查第3595行附近的修复
        lines = content.split('\n')
        
        # 查找修复的行
        fixed_line = None
        for i, line in enumerate(lines):
            if 'symbol = model_info.get(' in line and 'XAUUSD' in line:
                fixed_line = i + 1
                break
        
        if fixed_line:
            print(f"✅ 找到修复的代码行：第{fixed_line}行")
            print(f"   内容：{lines[fixed_line-1].strip()}")
            
            # 检查是否还有错误的用法
            error_lines = []
            for i, line in enumerate(lines):
                if 'symbol = model.get(' in line and 'XAUUSD' in line:
                    error_lines.append(i + 1)
            
            if error_lines:
                print(f"❌ 仍有错误用法在第{error_lines}行")
                return False
            else:
                print("✅ 没有发现错误的model.get()用法")
                return True
        else:
            print("❌ 没有找到修复的代码")
            return False
            
    except Exception as e:
        print(f"❌ 代码检查失败：{e}")
        return False

def main():
    """主函数"""
    print("🔧 LSTMModel 'get' 属性错误修复验证")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("ERROR: 'LSTMModel' object has no attribute 'get'")
    print("原因：在_load_and_run_pytorch_model函数中，错误地对PyTorch模型对象调用.get()方法")
    print("修复：将model.get()改为model_info.get()")
    
    # 执行测试
    test1_success = test_lstm_model_fix()
    test2_success = test_code_fix_directly()
    
    print(f"\n📊 测试结果总结")
    print("=" * 80)
    
    if test1_success and test2_success:
        print("🎉 修复验证成功！")
        print("✅ 模型类型验证通过")
        print("✅ 代码修复验证通过")
        print("✅ 错误已修复：model.get() → model_info.get()")
        
        print(f"\n💡 修复详情:")
        print("• 问题位置：services/deep_learning_service.py 第3595行")
        print("• 修复前：symbol = model.get('symbol', 'XAUUSD')")
        print("• 修复后：symbol = model_info.get('symbol', 'XAUUSD')")
        print("• 原因：model是PyTorch模型对象，model_info是包含元信息的字典")
        
    else:
        print("❌ 修复验证失败")
        if not test1_success:
            print("❌ 模型类型测试失败")
        if not test2_success:
            print("❌ 代码修复测试失败")

if __name__ == '__main__':
    main()
