#!/usr/bin/env python3
"""
测试回调交易功能
"""

import requests
import json
from datetime import datetime, timedelta

def login_session():
    """创建登录会话"""
    session = requests.Session()

    try:
        # 获取登录页面
        login_page = session.get('http://127.0.0.1:5000/login')

        # 登录（使用默认管理员账户）
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }

        response = session.post('http://127.0.0.1:5000/login', data=login_data)

        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print("❌ 登录失败")
            return None

    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_callback_backtest():
    """测试回调交易回测功能"""
    
    print("🧪 测试回调交易回测功能")
    print("=" * 50)
    
    # 回测配置
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)  # 最近30天
    
    config = {
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        # 资金配置
        'initial_capital': 10000,
        'lot_size': 0.1,
        'risk_percent': 2.0,
        'max_positions': 3,
        # 策略参数
        'trend_period': 20,
        'callback_percent': 38.2,
        'stop_loss_percent': 2.0,
        'take_profit_percent': 4.0
    }
    
    print(f"📊 回测配置:")
    print(f"   品种: {config['symbol']}")
    print(f"   时间框架: {config['timeframe']}")
    print(f"   回测期间: {config['start_date']} 到 {config['end_date']}")
    print(f"   初始资金: ${config['initial_capital']:,}")
    print(f"   每笔手数: {config['lot_size']}")
    print(f"   风险比例: {config['risk_percent']}%")
    print(f"   回调幅度: {config['callback_percent']}%")
    
    try:
        # 创建登录会话
        session = login_session()
        if not session:
            print("❌ 无法登录，跳过回测测试")
            return

        # 发送回测请求
        print(f"\n🔄 发送回测请求...")

        url = 'http://127.0.0.1:5000/api/callback-trading/backtest'
        headers = {'Content-Type': 'application/json'}

        response = session.post(url, json=config, headers=headers, timeout=60)
        
        print(f"📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"✅ 回测成功完成!")
                display_backtest_results(result['results'])
            else:
                print(f"❌ 回测失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时，回测可能需要更长时间")
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接错误，请确保应用程序正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def display_backtest_results(results):
    """显示回测结果"""
    
    print(f"\n📈 回测结果详情")
    print("=" * 50)
    
    # 基础统计
    print(f"📊 交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    print(f"   盈利次数: {results['winning_trades']}")
    print(f"   亏损次数: {results['losing_trades']}")
    print(f"   胜率: {results['win_rate']:.1f}%")
    
    # 资金统计
    print(f"\n💰 资金统计:")
    print(f"   初始资金: ${results['initial_capital']:,.2f}")
    print(f"   最终资金: ${results['final_balance']:,.2f}")
    print(f"   净盈亏: ${results['total_profit']:,.2f}")
    
    # 计算收益率
    return_rate = (results['final_balance'] - results['initial_capital']) / results['initial_capital'] * 100
    print(f"   收益率: {return_rate:.2f}%")
    
    # 盈亏统计
    print(f"\n📈 盈亏分析:")
    print(f"   总盈利: ${results['gross_profit']:,.2f}")
    print(f"   总亏损: ${abs(results['gross_loss']):,.2f}")
    print(f"   最大回撤: {results['max_drawdown']:.2f}%")
    
    if results['winning_trades'] > 0 and results['losing_trades'] > 0:
        profit_factor = results['gross_profit'] / abs(results['gross_loss'])
        print(f"   盈亏比: {profit_factor:.2f}")
    
    # 平均统计
    print(f"\n📊 平均统计:")
    print(f"   平均盈利: ${results['avg_win']:,.2f}")
    print(f"   平均亏损: ${abs(results['avg_loss']):,.2f}")
    print(f"   平均持仓时间: {results['avg_trade_duration']:.1f} 小时")
    
    # 数据来源
    print(f"\n📡 数据来源:")
    print(f"   品种: {results['symbol']}")
    print(f"   时间框架: {results['timeframe']}")
    print(f"   数据点数: {results['data_points']}")
    print(f"   回测期间: {results['start_date']} 到 {results['end_date']}")
    print(f"   数据来源: MT5真实历史数据")
    
    # 策略评估
    print(f"\n🎯 策略评估:")
    
    if results['win_rate'] >= 60:
        win_rating = "优秀"
    elif results['win_rate'] >= 50:
        win_rating = "良好"
    elif results['win_rate'] >= 40:
        win_rating = "一般"
    else:
        win_rating = "较差"
    
    print(f"   胜率评级: {win_rating}")
    
    if return_rate >= 10:
        return_rating = "优秀"
    elif return_rate >= 5:
        return_rating = "良好"
    elif return_rate >= 0:
        return_rating = "盈利"
    else:
        return_rating = "亏损"
    
    print(f"   收益评级: {return_rating}")
    
    if results['max_drawdown'] <= 5:
        risk_rating = "低风险"
    elif results['max_drawdown'] <= 15:
        risk_rating = "中等风险"
    else:
        risk_rating = "高风险"
    
    print(f"   风险评级: {risk_rating}")
    
    # 总体评价
    if results['total_trades'] >= 10:
        if results['win_rate'] >= 50 and return_rate > 0 and results['max_drawdown'] <= 20:
            overall = "✅ 策略表现良好，建议考虑实盘交易"
        elif results['win_rate'] >= 40 and return_rate > 0:
            overall = "⚠️ 策略表现一般，建议优化参数"
        else:
            overall = "❌ 策略表现较差，不建议实盘交易"
    else:
        overall = "📊 交易次数较少，建议延长回测期间"
    
    print(f"\n🎯 总体评价: {overall}")

def test_callback_trading_status():
    """测试回调交易状态API"""
    
    print(f"\n🔍 测试回调交易状态API")
    print("-" * 30)
    
    try:
        # 创建登录会话
        session = login_session()
        if not session:
            print("❌ 无法登录，跳过状态测试")
            return

        url = 'http://127.0.0.1:5000/api/callback-trading/status'
        response = session.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                status = result.get('status', {})
                monitoring = result.get('monitoring', {})
                
                print(f"✅ 状态API正常")
                print(f"   运行状态: {'运行中' if status.get('running') else '已停止'}")
                
                if status.get('running'):
                    print(f"   运行时间: {status.get('running_time', '--')}")
                    print(f"   今日交易: {status.get('today_trades', 0)} 次")
                    print(f"   监控品种: {status.get('symbol', '--')}")
                    print(f"   当前价格: {monitoring.get('current_price', '--')}")
                    print(f"   趋势方向: {monitoring.get('trend', '--')}")
            else:
                print(f"❌ 状态API错误: {result.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 状态API测试失败: {e}")

def main():
    """主函数"""
    
    print("🧪 回调交易功能测试")
    print("=" * 60)
    
    # 测试回测功能
    test_callback_backtest()
    
    # 测试状态API
    test_callback_trading_status()
    
    print(f"\n🎉 测试完成!")
    print("=" * 60)

if __name__ == '__main__':
    main()
