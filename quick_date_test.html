
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速日期选择测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>快速日期选择功能测试</h2>
        
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="backtestStartDate">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="backtestEndDate">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">
                            <i class="fas fa-clock me-2"></i>
                            快速时间选择
                        </label>
                        <div class="d-grid gap-2">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(1)">
                                    <i class="fas fa-calendar-day me-1"></i>1天
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(3)">
                                    <i class="fas fa-calendar-alt me-1"></i>3天
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(7)">
                                    <i class="fas fa-calendar-week me-1"></i>1周
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(30)">
                                    <i class="fas fa-calendar me-1"></i>1月
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(60)">
                                    <i class="fas fa-calendar-plus me-1"></i>2月
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-success" onclick="testQuickDateSelection()">运行测试</button>
            </div>
        </div>
        
        <div class="mt-3">
            <div class="alert alert-info">
                <h6>测试说明：</h6>
                <ul class="mb-0">
                    <li>点击快速时间选择按钮，观察日期输入框的变化</li>
                    <li>点击"运行测试"按钮，在浏览器控制台查看测试结果</li>
                    <li>验证计算的日期范围是否正确</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 快速设置日期范围
        function setQuickDateRange(days) {
            const endDate = new Date();
            const startDate = new Date();
            
            startDate.setDate(endDate.getDate() - days);
            
            const formatDate = (date) => {
                return date.toISOString().split('T')[0];
            };
            
            document.getElementById('backtestEndDate').value = formatDate(endDate);
            document.getElementById('backtestStartDate').value = formatDate(startDate);
            
            const buttons = document.querySelectorAll('.btn-group .btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            const clickedButton = event.target.closest('button');
            if (clickedButton) {
                clickedButton.classList.add('active');
                setTimeout(() => {
                    clickedButton.classList.remove('active');
                }, 2000);
            }
            
            console.log(`📅 设置日期范围: ${formatDate(startDate)} 到 ${formatDate(endDate)} (${getDaysText(days)})`);
        }
        
        function getDaysText(days) {
            switch(days) {
                case 1: return '1天';
                case 3: return '3天';
                case 7: return '1周';
                case 30: return '1个月';
                case 60: return '2个月';
                default: return `${days}天`;
            }
        }
        
        function testQuickDateSelection() {
            console.log('🧪 开始测试快速日期选择功能');
            
            const testCases = [
                {days: 1, description: '1天'},
                {days: 3, description: '3天'},
                {days: 7, description: '1周'},
                {days: 30, description: '1月'},
                {days: 60, description: '2月'}
            ];
            
            testCases.forEach((testCase, index) => {
                setTimeout(() => {
                    console.log(`\n🔹 测试 ${testCase.description}:`);
                    
                    setQuickDateRange(testCase.days);
                    
                    const startDate = document.getElementById('backtestStartDate').value;
                    const endDate = document.getElementById('backtestEndDate').value;
                    
                    console.log(`   开始日期: ${startDate}`);
                    console.log(`   结束日期: ${endDate}`);
                    
                    const start = new Date(startDate);
                    const end = new Date(endDate);
                    const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
                    
                    console.log(`   实际天数: ${diffDays} 天`);
                    console.log(`   预期天数: ${testCase.days} 天`);
                    console.log(`   ${diffDays === testCase.days ? '✅ 通过' : '❌ 失败'}`);
                }, index * 1000);
            });
        }
        
        // 页面加载时设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - 7); // 默认1周
            
            document.getElementById('backtestEndDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('backtestStartDate').value = startDate.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
