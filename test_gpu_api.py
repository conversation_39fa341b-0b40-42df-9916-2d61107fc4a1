#!/usr/bin/env python3
"""
测试GPU API
"""

import requests
import json

def test_gpu_api():
    """测试GPU状态API"""
    
    print("🎮 测试GPU状态API")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
        # 测试GPU状态API
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        print(f"📊 API响应状态: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📊 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('success'):
                    print(f"✅ API调用成功")
                    gpu_status = result.get('gpu_status', {})
                    print(f"   GPU可用: {gpu_status.get('gpu_available')}")
                    print(f"   GPU名称: {gpu_status.get('gpu_name')}")
                    print(f"   设备名称: {gpu_status.get('device_name')}")
                else:
                    print(f"❌ API返回错误: {result.get('error')}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_gpu_api()
