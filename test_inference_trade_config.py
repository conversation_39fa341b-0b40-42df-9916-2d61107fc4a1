#!/usr/bin/env python3
"""
测试AI推理交易配置功能
"""

import requests
import json
import time

def test_inference_with_trade_config():
    """测试带交易配置的AI推理"""
    
    print("🔧 测试AI推理交易配置功能")
    print("=" * 70)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        
        # 测试不同的交易配置
        test_configs = [
            {
                'name': '保守型交易配置',
                'trade_config': {
                    'trade_size': 0.01,
                    'min_confidence': 0.2,
                    'stop_loss_pips': 30,
                    'take_profit_pips': 60,
                    'trade_mode': 'signal_only',
                    'dynamic_sl': True,
                    'trailing_stop': False
                }
            },
            {
                'name': '平衡型交易配置',
                'trade_config': {
                    'trade_size': 0.01,
                    'min_confidence': 0.1,
                    'stop_loss_pips': 50,
                    'take_profit_pips': 100,
                    'trade_mode': 'semi_auto',
                    'dynamic_sl': True,
                    'trailing_stop': True
                }
            },
            {
                'name': '激进型交易配置',
                'trade_config': {
                    'trade_size': 0.02,
                    'min_confidence': 0.05,
                    'stop_loss_pips': 80,
                    'take_profit_pips': 150,
                    'trade_mode': 'auto_trade',
                    'dynamic_sl': True,
                    'trailing_stop': True
                }
            },
            {
                'name': '无配置（使用默认）',
                'trade_config': None
            }
        ]
        
        results = []
        
        for config in test_configs:
            print(f"\n🔍 测试: {config['name']}")
            
            if config['trade_config']:
                tc = config['trade_config']
                print(f"   交易手数: {tc['trade_size']}")
                print(f"   最低置信度: {tc['min_confidence']*100:.0f}%")
                print(f"   止损/止盈: {tc['stop_loss_pips']}/{tc['take_profit_pips']} pips")
                print(f"   交易模式: {tc['trade_mode']}")
                print(f"   动态止损: {'启用' if tc['dynamic_sl'] else '禁用'}")
            else:
                print("   使用默认配置")
            
            # 构建推理请求
            inference_data = {
                'model_id': test_model['id'],
                'symbol': test_model['symbol'],
                'timeframe': test_model['timeframe'],
                'data_points': 50,
                'inference_mode': 'realtime',
                'use_gpu': True,
                'show_confidence': True,
                'trade_config': config['trade_config']
            }
            
            start_time = time.time()
            response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                                   json=inference_data,
                                   headers={'Content-Type': 'application/json'})
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    predictions = result.get('results', [])
                    trade_config = result.get('trade_config', {})
                    
                    print(f"   ✅ 推理成功")
                    print(f"   📊 预测数量: {len(predictions)} 个")
                    print(f"   ⏱️ 执行时间: {end_time - start_time:.2f}秒")
                    
                    # 验证交易配置是否正确传递
                    if config['trade_config']:
                        expected_config = config['trade_config']
                        if (trade_config.get('trade_size') == expected_config['trade_size'] and
                            trade_config.get('min_confidence') == expected_config['min_confidence']):
                            print(f"   ✅ 交易配置正确传递")
                        else:
                            print(f"   ❌ 交易配置传递错误")
                            print(f"      期望: {expected_config}")
                            print(f"      实际: {trade_config}")
                    else:
                        # 验证默认配置
                        if trade_config.get('trade_size') == 0.01:
                            print(f"   ✅ 使用默认配置")
                        else:
                            print(f"   ❌ 默认配置错误")
                    
                    # 显示前几个预测结果
                    if len(predictions) > 0:
                        print(f"   🎯 预测结果 (前3个):")
                        for i, pred in enumerate(predictions[:3], 1):
                            confidence = pred.get('confidence', 0) * 100
                            print(f"      {i}. {pred.get('prediction')} @ {pred.get('current_price', 0):.5f} (置信度: {confidence:.1f}%)")
                    
                    results.append({
                        'config': config['name'],
                        'predictions': len(predictions),
                        'trade_config': trade_config,
                        'success': True
                    })
                    
                else:
                    print(f"   ❌ 推理失败: {result.get('error')}")
                    results.append({
                        'config': config['name'],
                        'success': False,
                        'error': result.get('error')
                    })
            else:
                print(f"   ❌ 请求失败: HTTP {response.status_code}")
                print(f"   响应: {response.text[:200]}")
                results.append({
                    'config': config['name'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
        
        # 分析测试结果
        if len(results) > 0:
            print(f"\n📊 测试结果汇总:")
            print(f"{'配置':<20} {'状态':<8} {'预测数':<8} {'交易手数':<10} {'置信度':<10}")
            print("-" * 70)
            
            for r in results:
                if r['success']:
                    tc = r.get('trade_config', {})
                    status = "✅成功"
                    predictions = r.get('predictions', 0)
                    trade_size = tc.get('trade_size', 'N/A')
                    min_conf = f"{tc.get('min_confidence', 0)*100:.0f}%" if tc.get('min_confidence') else 'N/A'
                else:
                    status = "❌失败"
                    predictions = 0
                    trade_size = 'N/A'
                    min_conf = 'N/A'
                
                print(f"{r['config']:<20} {status:<8} {predictions:<8} {trade_size:<10} {min_conf:<10}")
            
            # 统计成功率
            success_count = sum(1 for r in results if r['success'])
            success_rate = success_count / len(results) * 100
            
            print(f"\n📈 测试统计:")
            print(f"• 总测试数: {len(results)}")
            print(f"• 成功数: {success_count}")
            print(f"• 成功率: {success_rate:.1f}%")
            
            return success_rate >= 75  # 75%以上成功率认为测试通过
        else:
            print(f"\n⚠️ 没有测试结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def show_feature_summary():
    """显示功能总结"""
    
    print(f"\n📋 AI推理交易配置功能总结")
    print("=" * 60)
    
    print("🎨 新增功能:")
    print("1. ✅ AI推理交易配置面板")
    print("   • 交易手数设置 (0.01-10)")
    print("   • 最低置信度设置 (0.05-0.99)")
    print("   • 止损/止盈设置 (10-1000 pips)")
    print("   • 交易模式选择 (信号/半自动/自动)")
    print("   • 动态止损和移动止损选项")
    
    print("\n2. ✅ 配置预设")
    print("   • 保守型: 置信度20%, 仅信号模式")
    print("   • 平衡型: 置信度10%, 半自动模式")
    print("   • 激进型: 置信度5%, 自动交易模式")
    print("   • 自定义: 完全用户控制")
    
    print("\n3. ✅ 交易模式")
    print("   • 仅信号提示: 只显示交易信号")
    print("   • 半自动交易: 显示信号并询问执行")
    print("   • 自动交易: 根据信号自动执行")
    
    print("\n4. ✅ 风险管理")
    print("   • 动态止盈止损: 基于市场波动性调整")
    print("   • 移动止损: 跟随价格移动保护利润")
    print("   • 配置验证: 防止不合理参数设置")
    
    print("\n🔧 技术实现:")
    print("• 前端: 完整的配置界面和预设选择")
    print("• 后端: API支持交易配置参数传递")
    print("• 验证: 实时参数验证和错误提示")
    print("• 显示: 推理结果包含配置信息")

def main():
    """主函数"""
    
    print("🔧 AI推理交易配置功能测试")
    print("=" * 80)
    
    # 显示功能总结
    show_feature_summary()
    
    # 测试配置功能
    success = test_inference_with_trade_config()
    
    print(f"\n📊 最终测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 AI推理交易配置测试成功!")
        print("✅ 用户现在可以自定义推理交易参数")
        print("✅ 不同配置产生不同的推理行为")
        print("✅ 配置预设功能正常工作")
        print("✅ 交易配置正确传递到后端")
        
        print(f"\n💡 使用指南:")
        print("1. 点击'推理配置'按钮打开配置面板")
        print("2. 选择预设配置或自定义参数")
        print("3. 设置交易手数和置信度阈值")
        print("4. 选择合适的交易模式")
        print("5. 启用所需的风险管理功能")
        print("6. 点击'开始推理'执行AI分析")
        
        print(f"\n🎯 配置建议:")
        print("• 新手: 保守型预设 + 仅信号模式")
        print("• 进阶: 平衡型预设 + 半自动模式")
        print("• 专业: 激进型预设 + 自动交易模式")
        print("• 测试: 使用小手数验证策略效果")
        
    else:
        print("❌ 测试失败")
        print("⚠️ AI推理交易配置可能需要进一步调试")
        
        print(f"\n🔧 故障排除:")
        print("• 检查前端配置界面JavaScript")
        print("• 验证后端API参数接收")
        print("• 确认配置传递和验证逻辑")
        print("• 测试不同配置组合")

if __name__ == '__main__':
    main()
