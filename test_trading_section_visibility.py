#!/usr/bin/env python3
"""
测试推理交易区域可见性
"""

import requests
from bs4 import BeautifulSoup

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_trading_section_visibility():
    """测试推理交易区域可见性"""
    
    print("🔍 测试推理交易区域可见性")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            print(f"✅ 推理页面加载成功")
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找交易区域
            trading_section = soup.find('div', {'id': 'tradingSection'})
            
            if trading_section:
                print(f"✅ 找到推理交易区域")
                
                # 检查是否有display: none样式
                style = trading_section.get('style', '')
                
                if 'display: none' in style or 'display:none' in style:
                    print(f"❌ 交易区域被隐藏 (style='{style}')")
                    return False
                else:
                    print(f"✅ 交易区域可见 (style='{style}')")
                
                # 检查交易区域的关键元素
                key_elements = [
                    ('AI推理交易', '标题'),
                    ('tradingLotSize', '交易手数输入框'),
                    ('maxPositions', '最大持仓数输入框'),
                    ('stopLossPips', '止损点数输入框'),
                    ('takeProfitPips', '止盈点数输入框'),
                    ('minConfidence', '最低置信度输入框'),
                    ('enableAutoTrading', '自动交易开关'),
                    ('currentBid', '当前买价显示'),
                    ('currentAsk', '当前卖价显示'),
                    ('startTradingBtn', '开始交易按钮'),
                    ('stopTradingBtn', '停止交易按钮'),
                    ('mt5ConnectionStatus', 'MT5连接状态')
                ]
                
                found_elements = 0
                missing_elements = []
                
                for element_id, description in key_elements:
                    if element_id in html_content:
                        print(f"   ✅ {description}: 存在")
                        found_elements += 1
                    else:
                        print(f"   ❌ {description}: 缺失")
                        missing_elements.append(description)
                
                print(f"\n📊 交易区域元素统计:")
                print(f"   找到元素: {found_elements}/{len(key_elements)}")
                print(f"   缺失元素: {len(missing_elements)}")
                
                if missing_elements:
                    print(f"   缺失列表: {', '.join(missing_elements)}")
                
                # 检查交易区域的位置
                print(f"\n📍 交易区域位置:")
                
                # 查找推理结果区域
                results_card = soup.find('div', {'id': 'resultsCard'})
                if results_card:
                    print(f"   ✅ 推理结果区域存在")
                else:
                    print(f"   ❌ 推理结果区域不存在")
                
                # 检查交易区域是否在推理结果区域之后
                if trading_section and results_card:
                    # 简单检查：交易区域的HTML位置是否在推理结果之后
                    trading_pos = html_content.find('id="tradingSection"')
                    results_pos = html_content.find('id="resultsCard"')
                    
                    if trading_pos > results_pos:
                        print(f"   ✅ 交易区域位于推理结果区域之后")
                    else:
                        print(f"   ⚠️ 交易区域位置可能不正确")
                
                return found_elements >= len(key_elements) * 0.8  # 80%的元素存在就算成功
                
            else:
                print(f"❌ 未找到推理交易区域 (id='tradingSection')")
                return False
                
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_trading_section_structure():
    """测试交易区域结构"""
    
    print(f"\n🏗️ 测试交易区域结构")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            trading_section = soup.find('div', {'id': 'tradingSection'})
            
            if trading_section:
                # 检查交易区域的结构
                structure_checks = [
                    ('card', '主卡片容器'),
                    ('card-header', '卡片头部'),
                    ('card-body', '卡片主体'),
                    ('交易配置', '配置区域标题'),
                    ('交易状态', '状态区域标题'),
                    ('实时市场数据', '市场数据区域'),
                    ('最新推理结果', '推理结果区域'),
                    ('交易统计', '统计区域'),
                ]
                
                structure_score = 0
                
                for check_item, description in structure_checks:
                    if check_item in str(trading_section):
                        print(f"   ✅ {description}: 存在")
                        structure_score += 1
                    else:
                        print(f"   ❌ {description}: 缺失")
                
                print(f"\n📊 结构完整性: {structure_score}/{len(structure_checks)} ({structure_score/len(structure_checks)*100:.1f}%)")
                
                return structure_score >= len(structure_checks) * 0.7  # 70%结构存在就算成功
                
            else:
                print(f"❌ 交易区域不存在")
                return False
                
        else:
            print(f"❌ 页面访问失败")
            return False
            
    except Exception as e:
        print(f"❌ 结构测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🤖 推理交易区域可见性测试")
    print("=" * 80)
    
    print("📋 测试目标:")
    print("• 验证推理交易区域是否可见")
    print("• 检查交易区域的关键元素")
    print("• 验证交易区域的结构完整性")
    print("• 确认交易区域位置正确")
    
    # 测试交易区域可见性
    visibility_ok = test_trading_section_visibility()
    
    # 测试交易区域结构
    structure_ok = test_trading_section_structure()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if visibility_ok and structure_ok:
        print(f"🎉 推理交易区域完全可见且结构完整!")
        print(f"✅ 交易区域可见性正常")
        print(f"✅ 交易区域结构完整")
        
        print(f"\n💡 访问方式:")
        print(f"1. 打开浏览器访问: http://127.0.0.1:5000/deep-learning/inference")
        print(f"2. 登录系统 (用户名: admin, 密码: admin123)")
        print(f"3. 在推理页面下方即可看到 'AI推理交易' 区域")
        print(f"4. 交易区域包含完整的参数配置和控制功能")
        
        print(f"\n🎯 功能说明:")
        print(f"• 交易区域现在始终可见，无需等待推理完成")
        print(f"• 包含完整的交易参数配置")
        print(f"• 实时显示MT5连接状态和市场数据")
        print(f"• 提供自动交易启停控制")
        print(f"• 支持风险管理和资金管理设置")
        
    else:
        print(f"⚠️ 推理交易区域存在问题")
        print(f"交易区域可见性: {'✅' if visibility_ok else '❌'}")
        print(f"交易区域结构: {'✅' if structure_ok else '❌'}")
        
        print(f"\n🔧 可能的问题:")
        if not visibility_ok:
            print(f"• 交易区域可能被隐藏或元素缺失")
            print(f"• 检查HTML中的display样式设置")
        if not structure_ok:
            print(f"• 交易区域结构不完整")
            print(f"• 检查HTML模板的完整性")
    
    print(f"\n📍 交易区域位置:")
    print(f"页面路径: /deep-learning/inference")
    print(f"区域位置: 推理配置区域下方")
    print(f"区域ID: tradingSection")
    print(f"区域标题: AI推理交易")

if __name__ == '__main__':
    main()
