#!/usr/bin/env python3
"""
修复深度学习训练进度卡住的问题
"""

import sqlite3
import json
import time
import requests
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def login_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def stop_stuck_training():
    """停止卡住的训练任务"""
    print("\n🛑 停止卡住的训练任务")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡住的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs
            FROM training_tasks 
            WHERE status = 'running' AND progress = 25.0 AND current_epoch = 0
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的训练任务")
            conn.close()
            return True
        
        print(f"📋 发现 {len(stuck_tasks)} 个卡住的训练任务:")
        
        session = login_session()
        if not session:
            conn.close()
            return False
        
        for task in stuck_tasks:
            task_id, model_id, status, progress, current_epoch, total_epochs = task
            
            print(f"\n🔹 停止任务: {task_id[:8]}...")
            print(f"   模型ID: {model_id}")
            print(f"   进度: {progress}%")
            
            try:
                # 调用停止API
                response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/stop')
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if result.get('success'):
                        print(f"   ✅ 停止请求成功")
                        
                        # 直接更新数据库状态
                        cursor.execute("""
                            UPDATE training_tasks 
                            SET status = 'stopped', updated_at = ?
                            WHERE id = ?
                        """, (datetime.now().isoformat(), task_id))
                        
                        conn.commit()
                        print(f"   ✅ 数据库状态已更新")
                        
                    else:
                        print(f"   ❌ 停止失败: {result.get('error')}")
                else:
                    print(f"   ❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 停止异常: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 停止卡住训练失败: {e}")
        return False

def restart_training_with_smaller_config():
    """使用更小的配置重新开始训练"""
    print("\n🚀 使用更小的配置重新开始训练")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 使用更保守的训练配置
    training_config = {
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'days': 30,  # 减少数据量
        'epochs': 20,  # 减少训练轮数
        'batch_size': 16,  # 减少批次大小
        'learning_rate': 0.001,
        'sequence_length': 20,  # 减少序列长度
        'hidden_size': 64,  # 减少隐藏层大小
        'num_layers': 2,  # 减少层数
        'dropout': 0.2,
        'early_stopping': True,
        'patience': 5,
        'validation_split': 0.2,
        'use_gpu': True
    }
    
    print("📊 训练配置:")
    for key, value in training_config.items():
        print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=training_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                # 监控前30秒的进度
                print(f"\n📊 监控训练启动 (30秒):")
                
                for i in range(6):  # 6次检查，每次5秒
                    time.sleep(5)
                    
                    try:
                        progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                        
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            
                            if progress_result.get('success'):
                                progress_data = progress_result['progress']
                                progress = progress_data.get('progress', 0)
                                epoch = progress_data.get('epoch', 0)
                                status = progress_data.get('status', 'unknown')
                                
                                print(f"[{(i+1)*5:2d}s] 进度: {progress}%, 轮次: {epoch}, 状态: {status}")
                                
                                # 检查是否超过了25%
                                if progress > 25:
                                    print(f"   ✅ 训练已开始，进度正常更新")
                                    return True
                            else:
                                print(f"[{(i+1)*5:2d}s] ❌ API错误: {progress_result.get('error')}")
                        else:
                            print(f"[{(i+1)*5:2d}s] ❌ HTTP错误: {progress_response.status_code}")
                            
                    except Exception as e:
                        print(f"[{(i+1)*5:2d}s] ❌ 监控异常: {e}")
                
                print(f"\n📈 训练启动监控完成")
                return True
                
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 重新启动训练失败: {e}")
        return False

def main():
    print("🔧 修复深度学习训练进度卡住问题")
    print("=" * 60)
    
    # 步骤1: 停止卡住的训练任务
    if not stop_stuck_training():
        print("❌ 停止卡住训练失败")
        return
    
    # 步骤2: 等待一下让系统稳定
    print("\n⏳ 等待系统稳定...")
    time.sleep(3)
    
    # 步骤3: 使用更小的配置重新开始训练
    if restart_training_with_smaller_config():
        print("\n✅ 修复完成！训练已重新启动")
        print("💡 建议:")
        print("1. 在前端页面监控训练进度")
        print("2. 如果问题再次出现，考虑进一步减少模型复杂度")
        print("3. 检查GPU内存使用情况")
    else:
        print("\n❌ 修复失败")
        print("💡 手动修复建议:")
        print("1. 重启应用程序: python app.py")
        print("2. 在前端页面手动停止所有训练任务")
        print("3. 使用更小的数据集和模型配置重新训练")

if __name__ == '__main__':
    main()
