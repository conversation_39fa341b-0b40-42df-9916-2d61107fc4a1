#!/usr/bin/env python3
"""
测试低风险交易回测修复
"""

import requests
import json
import time

def test_low_risk_backtest():
    """测试低风险交易回测功能"""
    
    print("🔧 测试低风险交易回测修复")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 测试不同的回测时间段
        test_cases = [
            {
                'name': '1天回测',
                'days': 1,
                'expected_trades': 1
            },
            {
                'name': '3天回测',
                'days': 3,
                'expected_trades': 3
            },
            {
                'name': '7天回测',
                'days': 7,
                'expected_trades': 7
            }
        ]
        
        results = []
        
        for case in test_cases:
            print(f"\n🔍 测试: {case['name']}")
            print(f"   回测天数: {case['days']} 天")
            
            # 构建回测请求
            backtest_data = {
                'symbol': 'XAUUSD',
                'timeframe': '15m',
                'days': case['days']
            }
            
            print(f"📤 发送回测请求...")
            start_time = time.time()
            
            try:
                response = session.post(
                    'http://127.0.0.1:5000/api/low-risk-trading/backtest',
                    json=backtest_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                print(f"📥 响应状态码: {response.status_code}")
                print(f"⏱️ 执行时间: {execution_time:.2f}秒")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        
                        if result.get('success'):
                            trades = result.get('trades', [])
                            statistics = result.get('statistics', {})
                            
                            print(f"   ✅ 回测成功")
                            print(f"   📊 交易数量: {len(trades)} 笔")
                            print(f"   💰 总盈亏: ${statistics.get('total_pnl', 0):.2f}")
                            print(f"   🎯 胜率: {statistics.get('win_rate', 0):.1f}%")
                            print(f"   📈 总收益率: {statistics.get('total_return', 0):.2f}%")
                            
                            # 显示前几笔交易
                            if len(trades) > 0:
                                print(f"   📋 交易明细 (前3笔):")
                                for i, trade in enumerate(trades[:3], 1):
                                    action = trade.get('action', 'unknown')
                                    pnl = trade.get('pnl', 0)
                                    confidence = trade.get('confidence', 0)
                                    status = "💰" if pnl > 0 else "💸" if pnl < 0 else "⚖️"
                                    print(f"      {i}. {status} {action.upper()} ${pnl:.2f} (置信度: {confidence:.1f}%)")
                            
                            results.append({
                                'case': case['name'],
                                'success': True,
                                'trades': len(trades),
                                'pnl': statistics.get('total_pnl', 0),
                                'win_rate': statistics.get('win_rate', 0),
                                'execution_time': execution_time
                            })
                            
                        else:
                            error_msg = result.get('error', '未知错误')
                            print(f"   ❌ 回测失败: {error_msg}")
                            
                            # 检查是否是new_gold_strategy错误
                            if 'new_gold_strategy' in error_msg:
                                print(f"   🔧 检测到new_gold_strategy模块错误，这应该已经修复")
                                return False
                            
                            results.append({
                                'case': case['name'],
                                'success': False,
                                'error': error_msg,
                                'execution_time': execution_time
                            })
                    
                    except json.JSONDecodeError as e:
                        print(f"   ❌ JSON解析失败: {e}")
                        print(f"   响应内容: {response.text[:200]}")
                        results.append({
                            'case': case['name'],
                            'success': False,
                            'error': f'JSON解析失败: {e}',
                            'execution_time': execution_time
                        })
                
                else:
                    print(f"   ❌ HTTP请求失败: {response.status_code}")
                    print(f"   响应内容: {response.text[:200]}")
                    results.append({
                        'case': case['name'],
                        'success': False,
                        'error': f'HTTP {response.status_code}',
                        'execution_time': execution_time
                    })
            
            except requests.exceptions.Timeout:
                print(f"   ❌ 请求超时")
                results.append({
                    'case': case['name'],
                    'success': False,
                    'error': '请求超时',
                    'execution_time': 30
                })
            
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
                results.append({
                    'case': case['name'],
                    'success': False,
                    'error': str(e),
                    'execution_time': 0
                })
        
        # 分析测试结果
        if len(results) > 0:
            print(f"\n📊 测试结果汇总:")
            print(f"{'测试用例':<12} {'状态':<8} {'交易数':<8} {'盈亏':<10} {'胜率':<8} {'耗时':<8}")
            print("-" * 65)
            
            success_count = 0
            for r in results:
                if r['success']:
                    status = "✅成功"
                    trades = r.get('trades', 0)
                    pnl = f"${r.get('pnl', 0):.2f}"
                    win_rate = f"{r.get('win_rate', 0):.1f}%"
                    exec_time = f"{r.get('execution_time', 0):.1f}s"
                    success_count += 1
                else:
                    status = "❌失败"
                    trades = 0
                    pnl = "N/A"
                    win_rate = "N/A"
                    exec_time = f"{r.get('execution_time', 0):.1f}s"
                
                print(f"{r['case']:<12} {status:<8} {trades:<8} {pnl:<10} {win_rate:<8} {exec_time:<8}")
            
            # 统计成功率
            success_rate = success_count / len(results) * 100
            
            print(f"\n📈 测试统计:")
            print(f"• 总测试数: {len(results)}")
            print(f"• 成功数: {success_count}")
            print(f"• 成功率: {success_rate:.1f}%")
            
            return success_rate >= 66  # 66%以上成功率认为修复成功
        else:
            print(f"\n⚠️ 没有测试结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_new_gold_strategy_module():
    """测试new_gold_strategy模块"""
    
    print(f"\n🧪 测试new_gold_strategy模块")
    print("=" * 40)
    
    try:
        # 导入模块
        from new_gold_strategy import generate_new_trading_signal
        print("✅ 成功导入new_gold_strategy模块")
        
        # 创建测试数据
        import numpy as np
        from datetime import datetime, timedelta
        
        test_data = []
        base_price = 2650
        for i in range(50):
            price = base_price + np.random.normal(0, 10)
            test_data.append({
                'close': price,
                'timestamp': datetime.now() - timedelta(minutes=i)
            })
        
        print(f"📊 创建测试数据: {len(test_data)} 个数据点")
        
        # 生成信号
        signal = generate_new_trading_signal(test_data, datetime.now(), {})
        
        print(f"✅ 成功生成交易信号:")
        print(f"   动作: {signal.get('action', 'unknown')}")
        print(f"   置信度: {signal.get('confidence', 0):.2f}")
        print(f"   推理: {signal.get('reasoning', 'N/A')}")
        print(f"   当前价格: {signal.get('current_price', 0):.2f}")
        
        # 验证信号格式
        required_fields = ['action', 'confidence', 'reasoning', 'timestamp']
        missing_fields = [field for field in required_fields if field not in signal]
        
        if missing_fields:
            print(f"❌ 信号格式不完整，缺少字段: {missing_fields}")
            return False
        else:
            print(f"✅ 信号格式完整")
            return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 模块测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 低风险交易回测修复测试")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("• 创建了缺失的new_gold_strategy.py模块")
    print("• 实现了完整的多指标交易信号生成系统")
    print("• 包含RSI、MACD、布林带等技术指标")
    print("• 添加了市场环境分析功能")
    print("• 提供了详细的信号推理逻辑")
    
    # 测试模块
    module_ok = test_new_gold_strategy_module()
    
    # 测试回测功能
    backtest_ok = test_low_risk_backtest()
    
    print(f"\n📊 最终测试结果")
    print("=" * 80)
    
    if module_ok and backtest_ok:
        print("🎉 低风险交易回测修复成功!")
        print("✅ new_gold_strategy模块正常工作")
        print("✅ 回测功能恢复正常")
        print("✅ 多指标信号生成系统运行良好")
        
        print(f"\n💡 修复成果:")
        print("• 解决了'No module named new_gold_strategy'错误")
        print("• 实现了完整的技术分析指标计算")
        print("• 提供了智能的交易信号生成逻辑")
        print("• 支持多种市场环境的信号识别")
        print("• 回测功能现在可以正常执行")
        
    elif module_ok:
        print("🎉 模块修复成功!")
        print("✅ new_gold_strategy模块正常工作")
        print("⚠️ 回测功能可能需要进一步检查")
        
    elif backtest_ok:
        print("🎉 回测功能正常!")
        print("✅ 回测API正常响应")
        print("⚠️ 模块可能需要进一步优化")
        
    else:
        print("❌ 修复失败")
        print("⚠️ 需要进一步排查问题")
        
        print(f"\n🔧 故障排除:")
        print("• 检查new_gold_strategy.py文件是否存在")
        print("• 验证模块导入路径是否正确")
        print("• 确认所有依赖包是否安装")
        print("• 检查MT5连接状态")

if __name__ == '__main__':
    main()
