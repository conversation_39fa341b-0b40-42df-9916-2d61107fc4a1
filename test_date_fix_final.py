#!/usr/bin/env python3
"""
最终验证日期修复结果
"""

import requests
import json

def test_model_details_dates():
    """测试模型详情中的日期显示"""
    
    print("🔍 测试模型详情中的日期显示")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        if not result.get('success'):
            print("❌ 模型列表API返回失败")
            return False
        
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        # 测试每个模型的详情
        for i, model in enumerate(completed_models, 1):
            model_id = model['id']
            model_name = model['name']
            
            print(f"\n{i}. 测试模型: {model_name} ({model_id[:8]}...)")
            
            # 获取模型详情
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/models/{model_id}')
            
            if response.status_code == 200:
                detail_result = response.json()
                if detail_result.get('success'):
                    model_detail = detail_result.get('model', {})
                    
                    # 检查基本信息
                    print(f"   📋 基本信息:")
                    print(f"      模型名称: {model_detail.get('name', 'N/A')}")
                    print(f"      交易品种: {model_detail.get('symbol', 'N/A')}")
                    print(f"      时间框架: {model_detail.get('timeframe', 'N/A')}")
                    print(f"      状态: {model_detail.get('status', 'N/A')}")
                    
                    # 检查数据信息
                    data_info = model_detail.get('data_info', {})
                    print(f"   📊 数据信息:")
                    print(f"      开始日期: {data_info.get('start_date', 'N/A')}")
                    print(f"      结束日期: {data_info.get('end_date', 'N/A')}")
                    print(f"      总样本数: {data_info.get('total_samples', 'N/A')}")
                    print(f"      训练样本: {data_info.get('training_samples', 'N/A')}")
                    print(f"      验证样本: {data_info.get('validation_samples', 'N/A')}")
                    print(f"      数据质量: {data_info.get('data_quality', 'N/A')}")
                    
                    # 验证日期是否正确
                    start_date = data_info.get('start_date')
                    end_date = data_info.get('end_date')
                    
                    if end_date == '2025-07-01':
                        print(f"   ✅ 结束日期正确: {end_date}")
                    else:
                        print(f"   ❌ 结束日期错误: {end_date} (期望: 2025-07-01)")
                        return False
                    
                    if start_date == '2024-07-01':
                        print(f"   ✅ 开始日期正确: {start_date}")
                    else:
                        print(f"   ❌ 开始日期错误: {start_date} (期望: 2024-07-01)")
                        return False
                    
                    # 检查性能指标
                    performance = model_detail.get('performance', {})
                    if performance:
                        print(f"   📈 性能指标:")
                        print(f"      准确率: {performance.get('accuracy', 'N/A')}")
                        print(f"      精确率: {performance.get('precision', 'N/A')}")
                        print(f"      召回率: {performance.get('recall', 'N/A')}")
                        print(f"      F1分数: {performance.get('f1_score', 'N/A')}")
                    
                else:
                    print(f"   ❌ 获取模型详情失败: {detail_result.get('error')}")
                    return False
            else:
                print(f"   ❌ 模型详情请求失败: HTTP {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_new_training_dates():
    """测试新训练是否使用正确的日期"""
    
    print(f"\n🧪 测试新训练的日期逻辑")
    print("=" * 50)
    
    # 导入深度学习服务进行测试
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from services.deep_learning_service import deep_learning_service
    
    # 模拟用户配置
    test_config = {
        'data_config': {
            'mode': 'days',
            'training_days': 365,
            'end_date': '2025-07-01',  # 用户选择的结束日期
            'symbol': 'XAUUSD',
            'timeframe': '1h'
        }
    }
    
    print(f"测试配置: {json.dumps(test_config, indent=2, ensure_ascii=False)}")
    
    try:
        # 测试日期范围计算
        date_range = deep_learning_service._get_date_range_info(test_config['data_config'])
        
        print(f"\n计算结果:")
        print(f"  开始日期: {date_range['start_date']}")
        print(f"  结束日期: {date_range['end_date']}")
        print(f"  训练天数: {date_range['days']}")
        print(f"  模式: {date_range['mode']}")
        
        # 验证结果
        if date_range['end_date'] == '2025-07-01':
            print(f"✅ 新训练将使用正确的结束日期")
            return True
        else:
            print(f"❌ 新训练仍会使用错误的结束日期: {date_range['end_date']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 AI模型训练日期修复最终验证")
    print("=" * 80)
    
    # 测试现有模型的详情显示
    existing_models_ok = test_model_details_dates()
    
    # 测试新训练的日期逻辑
    new_training_ok = test_new_training_dates()
    
    print(f"\n📊 最终验证结果")
    print("=" * 80)
    
    if existing_models_ok and new_training_ok:
        print("🎉 所有测试通过!")
        print("✅ 现有模型的日期显示已修复")
        print("✅ 新训练将使用正确的日期逻辑")
        
        print(f"\n💡 修复总结:")
        print("• 修复了训练过程中的日期信息保存逻辑")
        print("• 更新了现有模型的日期信息")
        print("• 确保配置中的用户选择日期被正确使用")
        print("• 统一了前端配置和后端处理的日期格式")
        
        print(f"\n🎯 用户体验改善:")
        print("• 模型详情页面现在显示用户选择的正确日期")
        print("• 训练数据范围与用户设置完全一致")
        print("• 消除了日期设置的逻辑矛盾")
        print("• 提高了系统的可信度和准确性")
        
    elif existing_models_ok:
        print("🎉 现有模型修复成功!")
        print("✅ 现有模型的日期显示已修复")
        print("⚠️ 新训练的日期逻辑可能需要进一步检查")
        
    elif new_training_ok:
        print("🎉 新训练逻辑正确!")
        print("✅ 新训练将使用正确的日期逻辑")
        print("⚠️ 现有模型的显示可能需要进一步修复")
        
    else:
        print("❌ 验证失败")
        print("⚠️ 日期修复仍有问题，需要进一步排查")
        
        print(f"\n🔧 故障排除建议:")
        print("• 检查数据库中的模型配置是否正确")
        print("• 验证训练任务日志的更新是否成功")
        print("• 确认前端和后端的日期格式一致")
        print("• 查看详细的服务器日志")

if __name__ == '__main__':
    main()
