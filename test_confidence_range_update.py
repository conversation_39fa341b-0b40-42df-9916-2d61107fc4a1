#!/usr/bin/env python3
"""
测试置信度范围更新
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.deep_learning_service import DeepLearningService

def test_confidence_range_update():
    """测试置信度范围更新"""
    print("🎯 测试置信度范围更新")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 生成参数组合
        print("🔄 生成参数组合...")
        combinations = dl_service._generate_parameter_combinations()
        
        if not combinations:
            print("❌ 无法生成参数组合")
            return False
        
        print(f"✅ 成功生成 {len(combinations):,} 个参数组合")
        
        # 检查置信度范围
        confidence_values = set()
        for combo in combinations:
            confidence_values.add(combo['min_confidence'])
        
        expected_confidence = {0.30, 0.40, 0.50, 0.60, 0.70, 0.80}
        actual_confidence = confidence_values
        
        print(f"\n📊 置信度范围分析:")
        print(f"   期望范围: {sorted(expected_confidence)}")
        print(f"   实际范围: {sorted(actual_confidence)}")
        print(f"   范围数量: {len(actual_confidence)} 个值")
        
        if actual_confidence == expected_confidence:
            print("✅ 置信度范围更新成功")
            
            # 统计每个置信度的使用次数
            confidence_count = {}
            for combo in combinations:
                conf = combo['min_confidence']
                confidence_count[conf] = confidence_count.get(conf, 0) + 1
            
            print(f"\n📈 置信度分布:")
            for conf in sorted(confidence_count.keys()):
                count = confidence_count[conf]
                percentage = (count / len(combinations)) * 100
                print(f"   {conf:.2f}: {count:,} 组合 ({percentage:.1f}%)")
            
            return True
        else:
            print("❌ 置信度范围不匹配")
            missing = expected_confidence - actual_confidence
            extra = actual_confidence - expected_confidence
            if missing:
                print(f"   缺失值: {sorted(missing)}")
            if extra:
                print(f"   多余值: {sorted(extra)}")
            return False
            
    except Exception as e:
        print(f"❌ 置信度范围测试失败: {e}")
        return False

def test_parameter_combinations_impact():
    """测试参数组合数量影响"""
    print("\n🧮 测试参数组合数量影响")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 计算理论组合数
        parameter_counts = {
            'lot_size': 4,  # [0.01, 0.02, 0.05, 0.1]
            'stop_loss_pips': 4,  # [20, 30, 50, 80]
            'take_profit_pips': 4,  # [40, 60, 100, 150]
            'min_confidence': 6,  # [0.30, 0.40, 0.50, 0.60, 0.70, 0.80] - 更新为6个
            'cliff_brake_enabled': 2,  # [False, True]
            'trailing_stop_enabled': 2,  # [False, True]
            'trailing_stop_distance': 4,  # [15, 20, 25, 30]
            'trailing_stop_step': 4  # [5, 10, 15, 20]
        }
        
        print("📊 参数范围统计:")
        theoretical_total = 1
        for param, count in parameter_counts.items():
            theoretical_total *= count
            print(f"   {param}: {count} 个值")
        
        print(f"\n🔢 理论总组合数: {theoretical_total:,}")
        
        # 生成实际组合
        combinations = dl_service._generate_parameter_combinations()
        actual_total = len(combinations)
        
        print(f"📈 实际组合数: {actual_total:,}")
        
        if actual_total > 0:
            filter_rate = (theoretical_total - actual_total) / theoretical_total * 100
            print(f"🔍 过滤率: {filter_rate:.1f}%")
            
            # 估算优化时间
            estimated_time = actual_total * 0.5 / 60  # 分钟
            print(f"⏱️ 估算优化时间: {estimated_time:.1f} 分钟")
            
            # 评估时间合理性
            if estimated_time < 30:
                time_status = "✅ 优秀"
            elif estimated_time < 60:
                time_status = "⚠️ 可接受"
            elif estimated_time < 120:
                time_status = "⚠️ 较长但可行"
            else:
                time_status = "❌ 过长"
            
            print(f"   时间评价: {time_status}")
            
            return True
        else:
            print("❌ 无法生成参数组合")
            return False
            
    except Exception as e:
        print(f"❌ 参数组合影响测试失败: {e}")
        return False

def test_confidence_distribution_analysis():
    """测试置信度分布分析"""
    print("\n📊 测试置信度分布分析")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 生成参数组合
        combinations = dl_service._generate_parameter_combinations()
        
        if not combinations:
            print("❌ 无法生成参数组合")
            return False
        
        # 分析置信度与其他参数的组合
        confidence_analysis = {}
        
        for combo in combinations:
            conf = combo['min_confidence']
            if conf not in confidence_analysis:
                confidence_analysis[conf] = {
                    'count': 0,
                    'lot_sizes': set(),
                    'stop_losses': set(),
                    'take_profits': set()
                }
            
            confidence_analysis[conf]['count'] += 1
            confidence_analysis[conf]['lot_sizes'].add(combo['lot_size'])
            confidence_analysis[conf]['stop_losses'].add(combo['stop_loss_pips'])
            confidence_analysis[conf]['take_profits'].add(combo['take_profit_pips'])
        
        print("📈 置信度详细分析:")
        for conf in sorted(confidence_analysis.keys()):
            data = confidence_analysis[conf]
            print(f"\n   置信度 {conf:.2f}:")
            print(f"     组合数量: {data['count']:,}")
            print(f"     手数选择: {sorted(data['lot_sizes'])}")
            print(f"     止损选择: {sorted(data['stop_losses'])}")
            print(f"     止盈选择: {sorted(data['take_profits'])}")
        
        # 验证分布均匀性
        counts = [data['count'] for data in confidence_analysis.values()]
        min_count = min(counts)
        max_count = max(counts)
        
        print(f"\n🔍 分布均匀性:")
        print(f"   最小组合数: {min_count:,}")
        print(f"   最大组合数: {max_count:,}")
        
        if max_count == min_count:
            print("   ✅ 完全均匀分布")
        else:
            variance = max_count - min_count
            variance_rate = variance / min_count * 100
            print(f"   差异: {variance:,} ({variance_rate:.1f}%)")
            
            if variance_rate < 10:
                print("   ✅ 分布基本均匀")
            else:
                print("   ⚠️ 分布不够均匀")
        
        return True
        
    except Exception as e:
        print(f"❌ 置信度分布分析失败: {e}")
        return False

def test_confidence_range_validation():
    """测试置信度范围验证"""
    print("\n✅ 测试置信度范围验证")
    print("=" * 60)
    
    expected_range = [0.30, 0.40, 0.50, 0.60, 0.70, 0.80]
    
    print("🎯 置信度范围设计理念:")
    print("   • 0.30 (30%) - 较低置信度，更多交易机会")
    print("   • 0.40 (40%) - 中低置信度，平衡机会与质量")
    print("   • 0.50 (50%) - 中等置信度，标准阈值")
    print("   • 0.60 (60%) - 中高置信度，提高交易质量")
    print("   • 0.70 (70%) - 高置信度，精选交易机会")
    print("   • 0.80 (80%) - 极高置信度，最严格筛选")
    
    print(f"\n📊 范围特点:")
    print(f"   范围跨度: {max(expected_range) - min(expected_range):.2f}")
    print(f"   步长: 0.10 (10%)")
    print(f"   选择数量: {len(expected_range)} 个")
    print(f"   覆盖区间: {min(expected_range):.2f} - {max(expected_range):.2f}")
    
    print(f"\n🎯 适用场景:")
    scenarios = [
        ("保守交易", "0.70-0.80", "高质量信号，低频交易"),
        ("平衡交易", "0.50-0.60", "平衡质量与频率"),
        ("积极交易", "0.30-0.40", "更多机会，需要风控")
    ]
    
    for scenario, range_str, description in scenarios:
        print(f"   • {scenario}: {range_str} - {description}")
    
    return True

def main():
    print("🎯 置信度范围更新测试")
    print("=" * 80)
    print("🔄 更新内容: 置信度范围从4个值扩展到6个值")
    print("📊 新范围: [0.30, 0.40, 0.50, 0.60, 0.70, 0.80]")
    print("=" * 80)
    
    # 测试1: 置信度范围更新
    range_ok = test_confidence_range_update()
    
    # 测试2: 参数组合数量影响
    impact_ok = test_parameter_combinations_impact()
    
    # 测试3: 置信度分布分析
    distribution_ok = test_confidence_distribution_analysis()
    
    # 测试4: 置信度范围验证
    validation_ok = test_confidence_range_validation()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    
    print(f"置信度范围更新: {'✅ 通过' if range_ok else '❌ 失败'}")
    print(f"组合数量影响: {'✅ 通过' if impact_ok else '❌ 失败'}")
    print(f"分布分析: {'✅ 通过' if distribution_ok else '❌ 失败'}")
    print(f"范围验证: {'✅ 通过' if validation_ok else '❌ 失败'}")
    
    if all([range_ok, impact_ok, distribution_ok, validation_ok]):
        print("\n🎉 置信度范围更新成功！")
        print("\n✅ 更新效果:")
        print("   • 置信度选择从4个增加到6个")
        print("   • 范围更精细：30%-80%，步长10%")
        print("   • 适应不同交易风格和策略")
        print("   • 提供更灵活的信号筛选选项")
        
        print("\n📊 参数优化改进:")
        print("   • 更多置信度组合可供测试")
        print("   • 更精确的最优参数发现")
        print("   • 适应不同市场条件的策略")
        print("   • 满足不同风险偏好的需求")
        
        print("\n🚀 使用建议:")
        print("   • 新手交易者：建议使用0.60-0.80")
        print("   • 经验交易者：可以尝试0.40-0.60")
        print("   • 高频交易者：可以考虑0.30-0.50")
        print("   • 根据回测结果选择最优置信度")
        
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == '__main__':
    main()
