#!/usr/bin/env python3
"""
测试时间框架修复
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_timeframe_fix():
    """测试时间框架修复"""
    
    print("🔧 测试时间框架修复")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    # 使用H1时间框架测试
    config = {
        'model_name': f'timeframe_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',  # 使用H1格式
        'epochs': 1,
        'batch_size': 4,
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 3,
        'features': ['close', 'volume']
    }
    
    print(f"📝 测试配置 (重点: timeframe = 'H1'):")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    try:
        print(f"\n🚀 启动训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                # 监控前30秒，看是否能通过时间框架检查
                print(f"\n📊 监控前30秒...")
                
                for i in range(15):  # 检查15次，每次2秒
                    time.sleep(2)
                    
                    try:
                        progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                        
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            
                            if progress_result.get('success'):
                                progress_data = progress_result['progress']
                                status = progress_data.get('status', 'unknown')
                                progress = progress_data.get('progress', 0)
                                logs = progress_data.get('logs')
                                
                                print(f"   第{i+1}次检查: 状态={status}, 进度={progress}%")
                                
                                # 检查是否有时间框架错误
                                if logs:
                                    try:
                                        log_data = json.loads(logs)
                                        if 'error' in log_data and '时间框架' in log_data['error']:
                                            print(f"   ❌ 时间框架错误: {log_data['error']}")
                                            return False
                                        elif log_data.get('stage') == 'data_fetching':
                                            print(f"   ✅ 通过时间框架检查，正在获取数据")
                                            return True
                                        elif log_data.get('stage') == 'data_fetched':
                                            print(f"   🎉 数据获取成功，时间框架修复有效!")
                                            return True
                                    except:
                                        pass
                                
                                if status == 'failed':
                                    print(f"   ❌ 训练失败")
                                    return False
                                elif progress > 15:
                                    print(f"   ✅ 进度超过15%，时间框架问题已解决")
                                    return True
                                    
                            else:
                                print(f"   ❌ 获取进度失败: {progress_result.get('error')}")
                        else:
                            print(f"   ❌ 进度API错误: {progress_response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ 监控异常: {e}")
                
                print(f"   ⏰ 30秒监控结束")
                return False
                
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 时间框架修复验证")
    print("=" * 80)
    
    success = test_timeframe_fix()
    
    print(f"\n📋 修复验证结果")
    print("=" * 80)
    
    if success:
        print(f"🎉 时间框架修复成功!")
        print(f"✅ 支持H1时间框架格式")
        print(f"✅ 时间框架转换正常")
        print(f"✅ 数据获取能够开始")
        
        print(f"\n💡 修复内容:")
        print(f"• 添加了H1, H4, D1等MT5标准格式支持")
        print(f"• 兼容1h, 4h, 1d等通用格式")
        print(f"• 添加了详细的错误日志和支持列表")
        print(f"• 提供了时间框架转换确认信息")
        
    else:
        print(f"⚠️ 时间框架修复可能不完整")
        print(f"✅ 基础修复已完成")
        print(f"❌ 可能还有其他问题")
        
        print(f"\n🔧 建议进一步检查:")
        print(f"• 查看应用程序控制台的时间框架转换日志")
        print(f"• 确认MT5模块正常工作")
        print(f"• 检查是否有其他配置问题")
    
    print(f"\n🎯 支持的时间框架格式")
    print("=" * 80)
    
    print(f"📊 现在支持以下时间框架格式:")
    print(f"• 分钟: 1m, M1, 5m, M5, 15m, M15, 30m, M30")
    print(f"• 小时: 1h, H1, 4h, H4")
    print(f"• 日线: 1d, D1, daily")
    print(f"• 周线: 1w, W1, weekly")
    print(f"• 月线: 1M, MN1, monthly")
    
    print(f"\n💡 用户使用建议:")
    print(f"• 推荐使用MT5标准格式: H1, H4, D1")
    print(f"• 也支持通用格式: 1h, 4h, 1d")
    print(f"• 如果遇到不支持的格式，会显示支持列表")

if __name__ == '__main__':
    main()
