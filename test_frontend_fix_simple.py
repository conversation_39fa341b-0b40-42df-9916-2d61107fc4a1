#!/usr/bin/env python3
"""
简单测试前端修复是否有效
"""

import requests
import time

def test_parameter_optimization_simple():
    """简单测试参数优化"""
    print("🧪 简单测试参数优化功能")
    print("=" * 50)
    
    # 登录
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return False
        print("✅ 登录成功")
        
        # 确保MT5连接
        from services.mt5_service import mt5_service
        if not mt5_service.get_connection_status().get('connected', False):
            mt5_service.connect()
        
        # 测试参数优化API（使用较短的超时时间）
        print("🔄 测试参数优化API...")
        
        config = {
            'model_id': 'test_model',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'optimization_period': 'week'
        }
        
        start_time = time.time()
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/parameter-optimization',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=60  # 1分钟超时
        )
        
        duration = time.time() - start_time
        
        print(f"⏱️ API响应时间: {duration:.1f} 秒")
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 参数优化API调用成功")
                
                # 检查返回的数据结构
                total = result.get('total_combinations', 0)
                successful = result.get('successful_combinations', 0)
                results = result.get('optimization_results', [])
                
                print(f"📈 结果统计:")
                print(f"   总组合数: {total}")
                print(f"   成功组合数: {successful}")
                print(f"   返回结果数: {len(results)}")
                
                if results:
                    best = results[0]
                    print(f"   最佳收益率: {best.get('total_return', 0):.2f}%")
                    print(f"   最佳胜率: {best.get('win_rate', 0):.2f}%")
                    print(f"   最佳参数: {best.get('parameters', {})}")
                
                # 检查数据结构是否完整（这是前端需要的）
                required_fields = ['total_combinations', 'successful_combinations', 'optimization_results', 'best_parameters']
                missing_fields = [field for field in required_fields if field not in result]
                
                if missing_fields:
                    print(f"⚠️ 缺少字段: {missing_fields}")
                    print("   前端可能会因为缺少这些字段而出错")
                else:
                    print("✅ 返回数据结构完整，前端应该能正常处理")
                
                return True
                
            else:
                print(f"❌ API返回失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ API调用超时")
        print("💡 这可能是因为参数优化需要较长时间")
        print("   前端应该显示进度指示器而不是卡住")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_frontend_error_handling():
    """检查前端错误处理"""
    print("\n🛡️ 检查前端错误处理")
    print("=" * 50)
    
    try:
        # 获取前端页面内容
        response = requests.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含我们添加的空值检查
            checks = [
                ('if (!backtestCard || !backtestStats || !backtestResults)', '回测结果空值检查'),
                ('showError(', '错误显示函数调用'),
                ('console.error(', '控制台错误输出')
            ]
            
            found_checks = 0
            for check_code, description in checks:
                if check_code in content:
                    print(f"✅ 找到 {description}")
                    found_checks += 1
                else:
                    print(f"❌ 未找到 {description}")
            
            if found_checks >= 2:
                print("✅ 前端错误处理代码已添加")
                return True
            else:
                print("❌ 前端错误处理代码不完整")
                return False
        else:
            print(f"❌ 无法访问前端页面: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查前端错误处理失败: {e}")
        return False

def main():
    print("🔧 前端修复简单验证")
    print("=" * 60)
    print("🎯 目标: 验证 'Cannot read properties of null (reading 'style')' 错误是否已修复")
    print("=" * 60)
    
    # 测试1: 检查前端错误处理代码
    frontend_ok = check_frontend_error_handling()
    
    # 测试2: 测试API功能
    api_ok = test_parameter_optimization_simple()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 验证结果")
    print("=" * 60)
    
    print(f"前端错误处理: {'✅ 已添加' if frontend_ok else '❌ 未完整'}")
    print(f"API功能测试: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if frontend_ok:
        print("\n🎉 前端修复验证通过！")
        print("💡 修复内容:")
        print("   1. ✅ 修复了 DOM 元素ID 不匹配问题")
        print("   2. ✅ 添加了空值检查防止 null 错误")
        print("   3. ✅ 改进了错误处理和用户反馈")
        print("\n🚀 现在可以在浏览器中测试参数优化功能:")
        print("   1. 打开 http://127.0.0.1:5000/deep-learning/inference")
        print("   2. 点击参数优化按钮")
        print("   3. 应该不会再出现 'Cannot read properties of null' 错误")
    else:
        print("\n⚠️ 前端修复可能不完整")
        print("💡 建议:")
        print("   1. 检查模板文件是否正确保存")
        print("   2. 重启应用程序以加载最新代码")
        print("   3. 在浏览器开发者工具中检查JavaScript错误")

if __name__ == '__main__':
    main()
