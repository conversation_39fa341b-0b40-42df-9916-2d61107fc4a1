#!/usr/bin/env python3
"""
测试AI推理交易修复是否有效
"""

import requests
import json

def login_session():
    """登录并返回session"""
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_trade_execution_after_fix(session):
    """测试修复后的交易执行"""
    print("🔧 测试修复后的交易执行")
    print("=" * 50)
    
    try:
        # 模拟您提供的推理结果
        test_trade_data = {
            'symbol': 'XAUUSD',
            'action': 'SELL',
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'inference_result': {
                'prediction': 'SELL',
                'confidence': 0.95,
                'current_price': 3289.42,
                'timestamp': '04:00:00'
            },
            'trading_config': {
                'lot_size': 0.01,
                'min_confidence': 0.3,  # 根据之前的修改
                'max_positions': 4,     # 根据之前的修改
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        }
        
        print(f"📊 测试数据:")
        print(f"   品种: {test_trade_data['symbol']}")
        print(f"   方向: {test_trade_data['action']}")
        print(f"   置信度: {test_trade_data['inference_result']['confidence']*100}%")
        print(f"   目标价格: {test_trade_data['inference_result']['current_price']}")
        
        print(f"\n🚀 发送交易执行请求...")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/execute-trade', 
                               json=test_trade_data)
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 交易执行成功！")
                
                # 检查订单信息
                if 'order' in result:
                    order = result['order']
                    print(f"   📋 订单信息:")
                    print(f"      订单号: {order.get('order', 'N/A')}")
                    print(f"      类型: {order.get('type', 'N/A')}")
                    print(f"      手数: {order.get('volume', 'N/A')}")
                    print(f"      价格: {order.get('price', 'N/A')}")
                
                return True
            else:
                error = result.get('error', '未知错误')
                print(f"   ❌ 交易执行失败: {error}")
                
                # 分析错误原因
                if 'MT5未连接' in error:
                    print(f"   💡 解决方案: 检查MT5终端是否运行并连接")
                elif '置信度' in error:
                    print(f"   💡 解决方案: 检查最低置信度配置")
                elif '持仓' in error:
                    print(f"   💡 解决方案: 检查当前持仓数是否已满")
                
                return False
        else:
            print(f"   ❌ HTTP请求失败")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试交易执行失败: {e}")
        return False

def check_mt5_service_fix():
    """检查MT5服务修复"""
    print("\n🔍 检查MT5服务修复")
    print("=" * 50)
    
    try:
        # 直接导入并测试MT5服务
        from services.mt5_service import mt5_service
        
        print(f"📊 MT5服务状态:")
        print(f"   连接状态: {mt5_service.connected}")
        
        # 测试is_connected方法
        try:
            is_connected = mt5_service.is_connected()
            print(f"   is_connected()方法: ✅ 可用")
            print(f"   连接检查结果: {is_connected}")
            return True
        except AttributeError as e:
            print(f"   is_connected()方法: ❌ 仍然缺失")
            print(f"   错误: {e}")
            return False
        except Exception as e:
            print(f"   is_connected()方法: ⚠️ 运行异常")
            print(f"   错误: {e}")
            return True  # 方法存在但运行有问题
            
    except Exception as e:
        print(f"❌ 检查MT5服务失败: {e}")
        return False

def provide_next_steps():
    """提供下一步操作建议"""
    print("\n💡 下一步操作建议")
    print("=" * 50)
    
    print("🔧 立即操作:")
    print("   1. 重启应用程序以加载修复")
    print("   2. 确保MT5终端正在运行")
    print("   3. 重新启动AI推理交易")
    print("   4. 等待下一个推理结果并观察是否下单")
    print()
    
    print("📊 监控要点:")
    print("   • 浏览器控制台不再有'is_connected'错误")
    print("   • 推理结果出现后应该有交易执行日志")
    print("   • 如果满足条件应该能看到订单生成")
    print()
    
    print("⚠️ 如果仍有问题:")
    print("   • 检查置信度配置（应该≤95%）")
    print("   • 检查持仓数限制（当前持仓<最大持仓数）")
    print("   • 检查交易时间段设置")
    print("   • 确认自动交易已启动")

def main():
    """主函数"""
    print("🔧 测试AI推理交易修复")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("• 添加了MT5Service.is_connected()方法")
    print("• 解决了'MT5Service' object has no attribute 'is_connected'错误")
    print("• 现在应该能够正常执行交易")
    
    # 1. 检查MT5服务修复
    mt5_fix_ok = check_mt5_service_fix()
    
    # 2. 登录并测试
    session = login_session()
    if session:
        # 3. 测试交易执行
        trade_ok = test_trade_execution_after_fix(session)
    else:
        trade_ok = False
    
    # 4. 提供建议
    provide_next_steps()
    
    # 5. 总结
    print(f"\n📊 测试结果总结")
    print("=" * 80)
    
    if mt5_fix_ok:
        print(f"✅ MT5服务修复: 成功")
    else:
        print(f"❌ MT5服务修复: 失败")
    
    if trade_ok:
        print(f"✅ 交易执行测试: 成功")
    else:
        print(f"❌ 交易执行测试: 失败")
    
    if mt5_fix_ok and trade_ok:
        print(f"\n🎉 修复成功！")
        print(f"💡 现在AI推理交易应该能够正常工作")
        print(f"   • 推理结果: SELL 95% → 应该执行交易")
        print(f"   • 不再出现is_connected错误")
        print(f"   • 满足条件时会自动下单")
    elif mt5_fix_ok:
        print(f"\n✅ 核心问题已修复")
        print(f"💡 交易执行可能因其他条件未满足而失败")
        print(f"   • 检查MT5连接状态")
        print(f"   • 检查交易配置和限制")
    else:
        print(f"\n❌ 修复可能不完整")
        print(f"💡 需要进一步检查代码修改")

if __name__ == '__main__':
    main()
