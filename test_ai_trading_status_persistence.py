#!/usr/bin/env python3
"""
测试AI推理交易状态持久化修复
"""

import requests
import json
import time

def login_session():
    """登录并返回session"""
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_status_api(session):
    """测试状态API"""
    print("\n🔍 测试自动交易状态API")
    print("=" * 50)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 状态API正常")
                print(f"   活跃状态: {result.get('active', False)}")
                
                if result.get('active') and result.get('model_info'):
                    model_info = result['model_info']
                    print(f"   交易模型: {model_info.get('name', 'N/A')}")
                    print(f"   模型ID: {model_info.get('id', 'N/A')}")
                    print(f"   交易品种: {model_info.get('symbol', 'N/A')}")
                
                return result
            else:
                print(f"   ❌ 状态API失败: {result.get('error')}")
                return None
        else:
            print(f"   ❌ HTTP请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试状态API失败: {e}")
        return None

def test_start_trading(session):
    """测试启动交易"""
    print("\n🚀 测试启动自动交易")
    print("=" * 50)
    
    # 首先获取可用模型
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        print(f"   使用模型: {test_model['name']}")
        
        # 启动交易
        trading_config = {
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'max_positions': 1,
            'min_confidence': 0.6
        }
        
        start_data = {
            'model_id': test_model['id'],
            'trading_config': trading_config
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/auto-trading/start', 
                               json=start_data)
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   启动响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 自动交易启动成功")
                print(f"   会话ID: {result.get('session_id', 'N/A')}")
                return True
            else:
                print(f"   ❌ 启动失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试启动交易失败: {e}")
        return False

def test_stop_trading(session):
    """测试停止交易"""
    print("\n🛑 测试停止自动交易")
    print("=" * 50)
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/auto-trading/stop')
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   停止响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 自动交易停止成功")
                return True
            else:
                print(f"   ❌ 停止失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试停止交易失败: {e}")
        return False

def test_status_persistence():
    """测试状态持久化"""
    print("\n🔄 测试状态持久化")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 1. 检查初始状态
    print("1️⃣ 检查初始状态...")
    initial_status = test_status_api(session)
    
    # 2. 如果有活跃交易，先停止
    if initial_status and initial_status.get('active'):
        print("2️⃣ 发现活跃交易，先停止...")
        test_stop_trading(session)
        time.sleep(2)
    
    # 3. 启动新的交易
    print("3️⃣ 启动新的自动交易...")
    if not test_start_trading(session):
        return False
    
    # 4. 等待一下，然后检查状态
    print("4️⃣ 等待2秒后检查状态...")
    time.sleep(2)
    
    active_status = test_status_api(session)
    if not active_status or not active_status.get('active'):
        print("❌ 启动后状态检查失败")
        return False
    
    # 5. 模拟页面刷新 - 重新创建session
    print("5️⃣ 模拟页面刷新（重新登录）...")
    new_session = login_session()
    if not new_session:
        return False
    
    # 6. 检查状态是否持久化
    print("6️⃣ 检查状态是否持久化...")
    restored_status = test_status_api(new_session)
    
    if restored_status and restored_status.get('active'):
        print("✅ 状态持久化成功！")
        print(f"   会话ID: {restored_status.get('session_id')}")
        print(f"   模型信息: {restored_status.get('model_info', {}).get('name', 'N/A')}")
        
        # 7. 清理：停止交易
        print("7️⃣ 清理：停止交易...")
        test_stop_trading(new_session)
        
        return True
    else:
        print("❌ 状态持久化失败！")
        return False

def main():
    """主函数"""
    print("🔧 AI推理交易状态持久化测试")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("1. 自动交易状态API测试")
    print("2. 启动/停止交易功能测试")
    print("3. 状态持久化验证")
    print("4. 页面刷新后状态恢复测试")
    
    # 执行测试
    success = test_status_persistence()
    
    print(f"\n📊 测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 状态持久化测试通过！")
        print("✅ 修复效果验证成功")
        print("✅ 页面刷新后状态能够正确恢复")
        
        print(f"\n💡 修复内容:")
        print("• 统一数据库表名（ai_trading_sessions）")
        print("• 改进前端状态恢复逻辑")
        print("• 添加UI状态同步机制")
        print("• 增加状态监控和自动恢复")
        
    else:
        print("❌ 状态持久化测试失败")
        print("⚠️ 需要进一步调试")
        
        print(f"\n🔧 可能的问题:")
        print("• 数据库表结构问题")
        print("• API接口异常")
        print("• 前端状态恢复逻辑问题")

if __name__ == '__main__':
    main()
