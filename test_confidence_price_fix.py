#!/usr/bin/env python3
"""
测试置信度和价格格式修复
"""

import requests
import json
import time

def test_confidence_and_price_format():
    """测试置信度和价格格式修复"""
    
    print("🔧 测试置信度和价格格式修复")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        print(f"✅ 使用模型: {test_model['name']}")
        
        # 测试多次推理，检查置信度和价格格式
        test_count = 5
        results = []
        
        for i in range(test_count):
            print(f"\n🔍 第{i+1}次推理测试")
            
            # 构建推理请求
            inference_data = {
                'model_id': test_model['id'],
                'symbol': test_model['symbol'],
                'timeframe': test_model['timeframe'],
                'data_points': 50,
                'inference_mode': 'realtime',
                'use_gpu': True,
                'show_confidence': True,
                'trade_config': {
                    'trade_size': 0.01,
                    'min_confidence': 0.05,
                    'stop_loss_pips': 50,
                    'take_profit_pips': 100,
                    'trade_mode': 'signal_only',
                    'dynamic_sl': True,
                    'trailing_stop': False
                }
            }
            
            start_time = time.time()
            response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                                   json=inference_data)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    predictions = result.get('results', [])
                    
                    if len(predictions) > 0:
                        prediction = predictions[0]
                        confidence = prediction.get('confidence', 0)
                        price_target = prediction.get('price_target', 0)
                        current_price = prediction.get('current_price', 0)
                        
                        print(f"   📊 推理结果:")
                        print(f"      预测: {prediction.get('prediction', 'N/A')}")
                        print(f"      置信度: {confidence*100:.1f}%")
                        print(f"      当前价格: {current_price}")
                        print(f"      目标价格: {price_target}")
                        
                        # 验证置信度范围
                        confidence_valid = 0 <= confidence <= 1
                        confidence_reasonable = confidence <= 0.95
                        
                        # 验证价格格式
                        price_format_valid = True
                        price_decimal_places = 0
                        
                        if isinstance(price_target, (int, float)):
                            price_str = str(price_target)
                            if '.' in price_str:
                                price_decimal_places = len(price_str.split('.')[1])
                                # XAUUSD应该是2位小数
                                if test_model['symbol'] == 'XAUUSD':
                                    price_format_valid = price_decimal_places <= 2
                                else:
                                    price_format_valid = price_decimal_places <= 5
                        
                        print(f"   ✅ 验证结果:")
                        print(f"      置信度范围: {'✅正常' if confidence_valid else '❌异常'} (0-100%)")
                        print(f"      置信度合理: {'✅合理' if confidence_reasonable else '❌过高'} (≤95%)")
                        print(f"      价格格式: {'✅正常' if price_format_valid else '❌异常'} ({price_decimal_places}位小数)")
                        
                        results.append({
                            'test_number': i + 1,
                            'success': True,
                            'confidence': confidence,
                            'confidence_valid': confidence_valid,
                            'confidence_reasonable': confidence_reasonable,
                            'price_target': price_target,
                            'price_format_valid': price_format_valid,
                            'price_decimal_places': price_decimal_places,
                            'execution_time': end_time - start_time
                        })
                    else:
                        print(f"   ⚠️ 没有预测结果")
                        results.append({
                            'test_number': i + 1,
                            'success': False,
                            'error': '没有预测结果'
                        })
                else:
                    print(f"   ❌ 推理失败: {result.get('error')}")
                    results.append({
                        'test_number': i + 1,
                        'success': False,
                        'error': result.get('error')
                    })
            else:
                print(f"   ❌ 请求失败: HTTP {response.status_code}")
                results.append({
                    'test_number': i + 1,
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
            
            # 间隔1秒
            if i < test_count - 1:
                time.sleep(1)
        
        # 分析测试结果
        if len(results) > 0:
            print(f"\n📊 修复效果分析:")
            print(f"{'测试':<6} {'状态':<8} {'置信度':<10} {'置信度范围':<12} {'价格格式':<10} {'小数位':<8}")
            print("-" * 70)
            
            success_count = 0
            confidence_valid_count = 0
            confidence_reasonable_count = 0
            price_format_valid_count = 0
            
            for r in results:
                if r['success']:
                    status = "✅成功"
                    confidence_pct = f"{r['confidence']*100:.1f}%"
                    confidence_range = "✅正常" if r['confidence_valid'] else "❌异常"
                    price_format = "✅正常" if r['price_format_valid'] else "❌异常"
                    decimal_places = f"{r['price_decimal_places']}位"
                    
                    success_count += 1
                    if r['confidence_valid']:
                        confidence_valid_count += 1
                    if r['confidence_reasonable']:
                        confidence_reasonable_count += 1
                    if r['price_format_valid']:
                        price_format_valid_count += 1
                else:
                    status = "❌失败"
                    confidence_pct = "N/A"
                    confidence_range = "N/A"
                    price_format = "N/A"
                    decimal_places = "N/A"
                
                print(f"#{r['test_number']:<5} {status:<8} {confidence_pct:<10} {confidence_range:<12} {price_format:<10} {decimal_places:<8}")
            
            # 统计修复效果
            success_rate = success_count / len(results) * 100
            confidence_valid_rate = confidence_valid_count / len(results) * 100
            confidence_reasonable_rate = confidence_reasonable_count / len(results) * 100
            price_format_valid_rate = price_format_valid_count / len(results) * 100
            
            print(f"\n📈 修复效果统计:")
            print(f"• 总测试数: {len(results)}")
            print(f"• 成功数: {success_count}")
            print(f"• 成功率: {success_rate:.1f}%")
            print(f"• 置信度范围正常率: {confidence_valid_rate:.1f}%")
            print(f"• 置信度合理率: {confidence_reasonable_rate:.1f}%")
            print(f"• 价格格式正常率: {price_format_valid_rate:.1f}%")
            
            # 显示置信度分布
            if success_count > 0:
                successful_results = [r for r in results if r['success']]
                confidences = [r['confidence'] * 100 for r in successful_results]
                avg_confidence = sum(confidences) / len(confidences)
                max_confidence = max(confidences)
                min_confidence = min(confidences)
                
                print(f"\n📊 置信度分布:")
                print(f"• 平均置信度: {avg_confidence:.1f}%")
                print(f"• 最高置信度: {max_confidence:.1f}%")
                print(f"• 最低置信度: {min_confidence:.1f}%")
                
                # 检查是否还有超过100%的置信度
                over_100_count = sum(1 for c in confidences if c > 100)
                if over_100_count == 0:
                    print(f"✅ 没有置信度超过100%的情况")
                else:
                    print(f"❌ 仍有{over_100_count}个置信度超过100%")
            
            # 判断修复是否成功
            fix_success = (
                success_rate >= 80 and
                confidence_valid_rate >= 90 and
                confidence_reasonable_rate >= 90 and
                price_format_valid_rate >= 90
            )
            
            return fix_success
        else:
            print(f"\n⚠️ 没有测试结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def show_fix_summary():
    """显示修复内容总结"""
    
    print(f"\n📋 置信度和价格格式修复总结")
    print("=" * 60)
    
    print("🔧 修复内容:")
    print("1. ✅ 置信度计算修复")
    print("   • 修复了 abs(price_change) * 500 导致的超高置信度")
    print("   • 改为 abs(price_change) * 50，合理范围内")
    print("   • 添加置信度验证函数，确保0-95%范围")
    
    print("\n2. ✅ 价格格式修复")
    print("   • 添加价格格式化函数 _format_price()")
    print("   • XAUUSD: 保留2位小数")
    print("   • JPY货币对: 保留3位小数")
    print("   • 其他货币对: 保留5位小数")
    
    print("\n3. ✅ 代码优化")
    print("   • 统一使用验证函数处理置信度")
    print("   • 统一使用格式化函数处理价格")
    print("   • 添加异常处理和默认值")
    
    print("\n🎯 修复前后对比:")
    print("修复前:")
    print("• 置信度: 274.0% ❌")
    print("• 目标价格: 3287.0177999999996 ❌")
    
    print("\n修复后:")
    print("• 置信度: ≤95% ✅")
    print("• 目标价格: 3287.02 ✅")

def main():
    """主函数"""
    
    print("🔧 置信度和价格格式修复测试")
    print("=" * 80)
    
    # 显示修复总结
    show_fix_summary()
    
    # 测试修复效果
    success = test_confidence_and_price_format()
    
    print(f"\n📊 最终测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 置信度和价格格式修复成功!")
        print("✅ 置信度不再超过100%")
        print("✅ 价格格式规范化")
        print("✅ 所有验证指标达标")
        
        print(f"\n💡 修复效果:")
        print("• 置信度范围: 0-95% (合理范围)")
        print("• 价格精度: 根据品种自动调整")
        print("• 显示效果: 清晰易读")
        print("• 计算准确: 避免浮点精度问题")
        
    else:
        print("❌ 修复测试失败")
        print("⚠️ 可能需要进一步调试")
        
        print(f"\n🔧 故障排除:")
        print("• 检查置信度计算公式")
        print("• 验证价格格式化函数")
        print("• 确认验证函数逻辑")
        print("• 测试不同市场条件")

if __name__ == '__main__':
    main()
