
// 在浏览器控制台中运行此代码来测试修复效果

// 测试日期差计算函数
function testCalculateDateDifference() {
    console.log("🧪 测试日期差计算");
    
    const testCases = [
        { start: '2025-07-28', end: '2025-07-28', expected: 1, name: '同一天' },
        { start: '2025-07-28', end: '2025-07-29', expected: 2, name: '连续两天' },
        { start: '2025-07-21', end: '2025-07-28', expected: 8, name: '一周' },
        { start: '2025-06-28', end: '2025-07-28', expected: 31, name: '一个月' },
        { start: '2024-12-31', end: '2025-01-01', expected: 2, name: '跨年' }
    ];
    
    testCases.forEach(testCase => {
        const start = new Date(testCase.start);
        const end = new Date(testCase.end);
        const result = calculateDateDifference(start, end);
        const status = result === testCase.expected ? '✅' : '❌';
        
        console.log(`${status} ${testCase.name}: ${testCase.start} 到 ${testCase.end}`);
        console.log(`   计算结果: ${result}天, 期望: ${testCase.expected}天`);
    });
}

// 测试数据点估算函数
function testEstimateDataPoints() {
    console.log("\n📈 测试数据点估算");
    
    const timeframes = ['1h', 'H1', '4h', 'H4', '1d', 'D1'];
    const days = [1, 7, 30];
    
    days.forEach(day => {
        console.log(`\n🗓️ ${day}天的数据:`);
        timeframes.forEach(tf => {
            const points = estimateDataPoints(day, tf);
            console.log(`   ${tf}: ${points}个数据点`);
        });
    });
}

// 运行测试
testCalculateDateDifference();
testEstimateDataPoints();
