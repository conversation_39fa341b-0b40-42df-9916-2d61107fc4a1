#!/usr/bin/env python3
"""
测试训练暂停功能修复
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def start_test_training():
    """启动测试训练"""
    
    print("🚀 启动测试训练")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return None
    
    # 配置一个较长的训练任务，便于测试暂停
    config = {
        'model_name': f'pause_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 200,  # 较多轮次
        'batch_size': 8,   # 较小批次，训练慢一些
        'learning_rate': 0.001,
        'validation_split': 0.2,
        'sequence_length': 60,  # 较长序列
        'features': ['close', 'volume'],
        'early_stopping': False,  # 禁用早停
        'patience': 50,
        'min_epochs': 10,
        'use_gpu': True,
        'save_checkpoints': True
    }
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试训练启动成功!")
                print(f"   任务ID: {task_id}")
                print(f"   配置: {config['epochs']}轮, 批次大小{config['batch_size']}")
                
                # 等待训练开始
                print(f"⏳ 等待训练开始...")
                time.sleep(8)
                
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return None

def test_pause_resume_cycle(task_id):
    """测试暂停-恢复循环"""
    
    print(f"\n⏸️▶️ 测试暂停-恢复循环 (任务: {task_id})")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 1. 检查初始状态
        print("1️⃣ 检查初始训练状态...")
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                control = result['control']
                print(f"   初始状态: 可控制={control.get('can_control')}, 暂停={control.get('is_paused')}")
            else:
                print(f"   ❌ 获取状态失败: {result.get('error')}")
        
        # 2. 测试暂停
        print(f"\n2️⃣ 测试暂停功能...")
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/pause')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"   ✅ 暂停请求成功: {result.get('message')}")
                
                # 等待暂停生效
                time.sleep(3)
                
                # 检查暂停状态
                response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                
                if response.status_code == 200:
                    status_result = response.json()
                    if status_result.get('success'):
                        control = status_result['control']
                        is_paused = control.get('is_paused')
                        print(f"   暂停状态: {is_paused}")
                        
                        if is_paused:
                            print(f"   ✅ 训练已成功暂停")
                        else:
                            print(f"   ⚠️ 暂停状态未生效")
                            return False
                    else:
                        print(f"   ❌ 获取暂停状态失败: {status_result.get('error')}")
                        return False
            else:
                print(f"   ❌ 暂停失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 暂停API请求失败: {response.status_code}")
            return False
        
        # 3. 等待一段时间（验证训练确实暂停）
        print(f"\n3️⃣ 验证训练暂停效果...")
        print(f"   等待10秒，检查训练是否真的暂停...")
        
        # 记录暂停前的进度
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        progress_before = 0
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                progress_before = result['progress'].get('progress', 0)
                print(f"   暂停前进度: {progress_before}%")
        
        time.sleep(10)
        
        # 检查暂停后的进度
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        progress_after = 0
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                progress_after = result['progress'].get('progress', 0)
                print(f"   暂停后进度: {progress_after}%")
        
        if progress_after == progress_before:
            print(f"   ✅ 训练确实已暂停 (进度未变化)")
        else:
            print(f"   ⚠️ 训练可能未完全暂停 (进度变化: {progress_after - progress_before}%)")
        
        # 4. 测试恢复
        print(f"\n4️⃣ 测试恢复功能...")
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/resume')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"   ✅ 恢复请求成功: {result.get('message')}")
                
                # 等待恢复生效
                time.sleep(3)
                
                # 检查恢复状态
                response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                
                if response.status_code == 200:
                    status_result = response.json()
                    if status_result.get('success'):
                        control = status_result['control']
                        is_paused = control.get('is_paused')
                        print(f"   恢复后暂停状态: {is_paused}")
                        
                        if not is_paused:
                            print(f"   ✅ 训练已成功恢复")
                            return True
                        else:
                            print(f"   ⚠️ 恢复状态未生效")
                            return False
                    else:
                        print(f"   ❌ 获取恢复状态失败: {status_result.get('error')}")
                        return False
            else:
                print(f"   ❌ 恢复失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 恢复API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试暂停-恢复循环失败: {e}")
        return False

def test_stop_functionality(task_id):
    """测试停止功能"""
    
    print(f"\n🛑 测试停止功能 (任务: {task_id})")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/stop')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"✅ 停止请求成功: {result.get('message')}")
                
                # 等待停止生效
                time.sleep(3)
                
                # 检查停止状态
                response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                
                if response.status_code == 200:
                    status_result = response.json()
                    if status_result.get('success'):
                        control = status_result['control']
                        is_stopped = control.get('is_stopped')
                        print(f"   停止状态: {is_stopped}")
                        
                        if is_stopped:
                            print(f"   ✅ 训练已成功停止")
                            return True
                        else:
                            print(f"   ⚠️ 停止状态未生效")
                            return False
                    else:
                        print(f"   ❌ 获取停止状态失败: {status_result.get('error')}")
                        return False
            else:
                print(f"❌ 停止失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 停止API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试停止功能失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 训练暂停功能修复验证")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("• 改进了暂停/恢复/停止功能的任务状态检查")
    print("• 支持重新注册丢失的训练控制状态")
    print("• 同步更新数据库状态")
    print("• 增强了控制状态获取功能")
    
    # 启动测试训练
    task_id = start_test_training()
    
    if not task_id:
        print(f"\n❌ 无法启动测试训练，测试终止")
        return
    
    # 测试暂停-恢复循环
    pause_resume_ok = test_pause_resume_cycle(task_id)
    
    # 测试停止功能
    stop_ok = test_stop_functionality(task_id)
    
    print(f"\n📋 修复验证结果")
    print("=" * 80)
    
    if pause_resume_ok and stop_ok:
        print(f"🎉 训练暂停功能修复完全成功!")
        print(f"✅ 暂停功能正常工作")
        print(f"✅ 恢复功能正常工作")
        print(f"✅ 停止功能正常工作")
        
        print(f"\n💡 修复成果:")
        print(f"• 解决了'训练任务不存在或已完成'错误")
        print(f"• 支持服务重启后的任务控制")
        print(f"• 数据库状态与内存状态同步")
        print(f"• 提供了详细的控制状态信息")
        
        print(f"\n🎯 用户体验改善:")
        print(f"• 可以正常暂停正在运行的训练")
        print(f"• 暂停后可以成功恢复训练")
        print(f"• 可以随时停止不需要的训练")
        print(f"• 控制按钮状态正确更新")
        
    else:
        print(f"⚠️ 部分功能可能仍有问题")
        print(f"暂停-恢复功能: {'✅' if pause_resume_ok else '❌'}")
        print(f"停止功能: {'✅' if stop_ok else '❌'}")
    
    print(f"\n🔧 使用说明")
    print("=" * 80)
    
    print(f"📊 训练控制功能:")
    print(f"• 暂停: 临时停止训练，可以恢复")
    print(f"• 恢复: 继续之前暂停的训练")
    print(f"• 停止: 永久停止训练，无法恢复")
    
    print(f"\n💡 最佳实践:")
    print(f"• 长时间训练时可以使用暂停功能休息")
    print(f"• 发现问题时及时停止训练")
    print(f"• 暂停状态不会持久化，服务重启后会丢失")
    print(f"• 建议在训练过程中保存检查点")

if __name__ == '__main__':
    main()
