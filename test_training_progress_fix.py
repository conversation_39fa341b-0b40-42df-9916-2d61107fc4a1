#!/usr/bin/env python3
"""
测试和修复训练进度更新问题
"""

import requests
import json
import time
import sqlite3

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def check_training_tasks_in_db():
    """检查数据库中的训练任务"""
    
    print("🔍 检查数据库中的训练任务")
    print("=" * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询所有训练任务
        cursor.execute('''
            SELECT id, status, progress, current_epoch, total_epochs, 
                   train_loss, val_loss, created_at, updated_at, logs
            FROM training_tasks 
            ORDER BY created_at DESC 
            LIMIT 10
        ''')
        
        tasks = cursor.fetchall()
        
        if tasks:
            print(f"✅ 找到 {len(tasks)} 个训练任务")
            
            for i, task in enumerate(tasks, 1):
                task_id, status, progress, current_epoch, total_epochs, train_loss, val_loss, created_at, updated_at, logs = task
                
                print(f"\n📋 任务 {i}: {task_id[:8]}...")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   训练损失: {train_loss}")
                print(f"   验证损失: {val_loss}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 解析日志
                if logs:
                    try:
                        logs_data = json.loads(logs)
                        print(f"   日志阶段: {logs_data.get('stage', 'N/A')}")
                        print(f"   日志消息: {logs_data.get('message', 'N/A')}")
                    except:
                        print(f"   日志: 解析失败")
                else:
                    print(f"   日志: 无")
                
                # 检查是否有正在运行的任务
                if status == 'running':
                    print(f"   🔄 发现正在运行的任务!")
                    return task_id
        else:
            print(f"❌ 没有找到训练任务")
            
        conn.close()
        return None
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return None

def test_training_progress_api(task_id):
    """测试训练进度API"""
    
    print(f"\n📡 测试训练进度API")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 测试进度API
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        print(f"📊 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📋 API响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                if result.get('success'):
                    progress = result.get('progress', {})
                    print(f"\n✅ 进度数据解析:")
                    print(f"   状态: {progress.get('status')}")
                    print(f"   进度: {progress.get('progress')}%")
                    print(f"   轮次: {progress.get('epoch')}/{progress.get('total_epochs')}")
                    print(f"   训练损失: {progress.get('train_loss')}")
                    print(f"   验证损失: {progress.get('val_loss')}")
                    print(f"   更新时间: {progress.get('updated_at')}")
                    
                    # 检查日志
                    logs = progress.get('logs')
                    if logs:
                        print(f"   日志数据: {logs}")
                    else:
                        print(f"   日志数据: 无")
                    
                    return True
                else:
                    print(f"❌ API返回失败: {result.get('error')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试API失败: {e}")
        return False

def simulate_progress_update(task_id):
    """模拟进度更新"""
    
    print(f"\n🔄 模拟进度更新")
    print("=" * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 模拟更新进度
        test_updates = [
            (10, 1, 10, 0.5, 0.6, '数据准备', '正在准备训练数据...'),
            (20, 2, 10, 0.4, 0.55, '模型训练', '训练进行中 - Epoch 2/10'),
            (30, 3, 10, 0.35, 0.5, '模型训练', '训练进行中 - Epoch 3/10'),
            (40, 4, 10, 0.3, 0.45, '模型训练', '训练进行中 - Epoch 4/10'),
        ]
        
        for progress, epoch, total_epochs, train_loss, val_loss, stage, message in test_updates:
            print(f"📊 更新进度: {progress}% (Epoch {epoch}/{total_epochs})")
            
            # 更新基本进度
            cursor.execute('''
                UPDATE training_tasks 
                SET progress = ?, current_epoch = ?, total_epochs = ?,
                    train_loss = ?, val_loss = ?, updated_at = datetime('now')
                WHERE id = ?
            ''', (progress, epoch, total_epochs, train_loss, val_loss, task_id))
            
            # 更新日志
            logs_data = {
                'stage': stage,
                'message': message,
                'epoch': epoch,
                'total_epochs': total_epochs,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'progress_percent': progress
            }
            
            cursor.execute('''
                UPDATE training_tasks 
                SET logs = ?
                WHERE id = ?
            ''', (json.dumps(logs_data), task_id))
            
            conn.commit()
            
            # 等待一下
            time.sleep(2)
            
            # 测试API响应
            session = login_session()
            if session:
                response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        api_progress = result['progress'].get('progress', 0)
                        print(f"   ✅ API返回进度: {api_progress}%")
                    else:
                        print(f"   ❌ API返回错误: {result.get('error')}")
                else:
                    print(f"   ❌ API HTTP错误: {response.status_code}")
        
        conn.close()
        print(f"✅ 模拟更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 模拟更新失败: {e}")
        return False

def check_frontend_updates():
    """检查前端更新机制"""
    
    print(f"\n🖥️ 检查前端更新机制")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问训练页面
        response = session.get('http://127.0.0.1:5000/deep-learning/training')
        
        if response.status_code == 200:
            html_content = response.text
            
            # 检查关键的JavaScript函数
            js_functions = [
                'updateTrainingProgress',
                'startProgressMonitoring',
                'stopProgressMonitoring',
                'trainingProgress',
                'progressInterval'
            ]
            
            print(f"🔍 检查JavaScript函数:")
            for func in js_functions:
                if func in html_content:
                    print(f"   ✅ {func}: 存在")
                else:
                    print(f"   ❌ {func}: 缺失")
            
            # 检查进度更新间隔
            if 'setInterval' in html_content and 'updateTrainingProgress' in html_content:
                print(f"   ✅ 进度更新定时器: 存在")
            else:
                print(f"   ❌ 进度更新定时器: 可能有问题")
            
            return True
        else:
            print(f"❌ 训练页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查前端失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 训练进度更新问题诊断和修复")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 深度学习模型训练进度没有变化")
    print("• 可能的原因:")
    print("  - 后端进度更新逻辑有问题")
    print("  - 前端进度获取机制有问题")
    print("  - 数据库更新不及时")
    print("  - API响应有问题")
    
    # 1. 检查数据库中的训练任务
    running_task_id = check_training_tasks_in_db()
    
    # 2. 如果有正在运行的任务，测试API
    if running_task_id:
        api_ok = test_training_progress_api(running_task_id)
        
        # 3. 模拟进度更新
        if api_ok:
            simulate_ok = simulate_progress_update(running_task_id)
        else:
            simulate_ok = False
    else:
        print(f"\n⚠️ 没有找到正在运行的训练任务")
        print(f"💡 建议: 启动一个新的训练任务来测试进度更新")
        api_ok = False
        simulate_ok = False
    
    # 4. 检查前端更新机制
    frontend_ok = check_frontend_updates()
    
    print(f"\n📋 诊断结果总结")
    print("=" * 80)
    
    if running_task_id and api_ok and simulate_ok and frontend_ok:
        print(f"🎉 训练进度更新机制正常!")
        print(f"✅ 数据库任务存在")
        print(f"✅ 进度API正常")
        print(f"✅ 进度更新成功")
        print(f"✅ 前端机制正常")
        
        print(f"\n💡 如果进度仍然不变化，可能的原因:")
        print(f"• 训练过程本身卡住了")
        print(f"• 训练数据加载有问题")
        print(f"• GPU资源不足")
        print(f"• 网络连接问题")
        
    else:
        print(f"⚠️ 发现问题，需要修复")
        print(f"数据库任务: {'✅' if running_task_id else '❌'}")
        print(f"进度API: {'✅' if api_ok else '❌'}")
        print(f"进度更新: {'✅' if simulate_ok else '❌'}")
        print(f"前端机制: {'✅' if frontend_ok else '❌'}")
        
        print(f"\n🔧 修复建议:")
        if not running_task_id:
            print(f"• 启动一个新的训练任务")
        if not api_ok:
            print(f"• 检查进度API的实现")
        if not simulate_ok:
            print(f"• 检查数据库更新逻辑")
        if not frontend_ok:
            print(f"• 检查前端JavaScript代码")
    
    print(f"\n🎯 实时监控建议")
    print("=" * 80)
    
    print(f"📊 监控训练进度:")
    print(f"• 打开浏览器开发者工具 (F12)")
    print(f"• 查看Console标签页的日志输出")
    print(f"• 查看Network标签页的API请求")
    print(f"• 观察进度API的响应内容")
    
    print(f"\n🔍 手动检查方法:")
    print(f"• 直接访问进度API: /api/deep-learning/training-progress/{{task_id}}")
    print(f"• 查看数据库: trading_system.db 中的 training_tasks 表")
    print(f"• 检查服务器日志输出")

if __name__ == '__main__':
    main()
