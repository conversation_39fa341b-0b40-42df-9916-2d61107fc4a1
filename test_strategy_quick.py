#!/usr/bin/env python3
"""
快速测试优化后的回调策略
"""

import requests
import json
from datetime import datetime, timedelta

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            return session
        else:
            return None
            
    except Exception as e:
        return None

def test_ultra_aggressive_strategy():
    """测试超激进策略"""
    
    print("🚀 测试超激进回调策略")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 登录失败")
        return
    
    # 使用最短时间范围和最激进参数
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)  # 仅3天数据
    
    config = {
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        # 资金配置
        'initial_capital': 10000,
        'lot_size': 0.01,
        'risk_percent': 2.0,
        'max_positions': 4,
        # 超激进参数
        'trend_period': 5,    # 最短趋势周期
        'callback_percent': 5.0,  # 极低回调要求
        'stop_loss_percent': 5.0,  # 宽松止损
        'take_profit_percent': 10.0   # 宽松止盈
    }
    
    print(f"📊 超激进配置:")
    print(f"   时间范围: {config['start_date']} 到 {config['end_date']}")
    print(f"   趋势周期: {config['trend_period']} (极短)")
    print(f"   回调要求: {config['callback_percent']}% (极低)")
    
    try:
        print(f"\n🔄 发送回测请求...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/callback-trading/backtest',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                results = result['results']
                print(f"✅ 回测成功!")
                
                print(f"\n📈 结果:")
                print(f"   数据点数: {results['data_points']}")
                print(f"   总交易次数: {results['total_trades']}")
                
                if results['total_trades'] > 0:
                    print(f"   🎉 成功产生交易信号!")
                    print(f"   盈利次数: {results['winning_trades']}")
                    print(f"   亏损次数: {results['losing_trades']}")
                    print(f"   胜率: {results['win_rate']:.1f}%")
                    print(f"   净盈亏: ${results['total_profit']:.2f}")
                    
                    return_rate = (results['final_balance'] - results['initial_capital']) / results['initial_capital'] * 100
                    print(f"   收益率: {return_rate:.2f}%")
                    
                    print(f"\n✅ 策略优化成功!")
                    return True
                else:
                    print(f"   ❌ 仍然没有交易信号")
                    print(f"   💡 需要进一步检查策略逻辑")
                    return False
                    
            else:
                print(f"❌ 回测失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_simple_ma_strategy():
    """测试简单MA策略"""
    
    print(f"\n📊 测试简单MA偏离策略")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return
    
    # 使用1天数据进行快速测试
    end_date = datetime.now()
    start_date = end_date - timedelta(days=1)
    
    config = {
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'initial_capital': 10000,
        'lot_size': 0.01,
        'risk_percent': 2.0,
        'max_positions': 4,
        # 简单参数
        'trend_period': 3,    # 极短周期
        'callback_percent': 1.0,  # 极低要求
        'stop_loss_percent': 10.0,
        'take_profit_percent': 20.0
    }
    
    print(f"📊 简单MA策略配置:")
    print(f"   时间范围: 1天")
    print(f"   趋势周期: 3")
    print(f"   回调要求: 1%")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/callback-trading/backtest',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                results = result['results']
                trades = results['total_trades']
                
                print(f"📈 结果: {trades} 次交易")
                
                if trades > 0:
                    print(f"✅ 简单策略产生了交易信号!")
                    print(f"   胜率: {results['win_rate']:.1f}%")
                    print(f"   净盈亏: ${results['total_profit']:.2f}")
                else:
                    print(f"❌ 简单策略也没有信号")
                    
            else:
                print(f"❌ 简单策略测试失败: {result.get('error')}")
        else:
            print(f"❌ 简单策略请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 简单策略测试异常: {e}")

def main():
    """主函数"""
    
    print("🧪 回调策略快速验证测试")
    print("=" * 60)
    
    # 测试超激进策略
    success = test_ultra_aggressive_strategy()
    
    # 测试简单MA策略
    test_simple_ma_strategy()
    
    print(f"\n📋 测试总结")
    print("=" * 60)
    
    if success:
        print(f"🎉 策略优化成功!")
        print(f"✅ 已能够产生交易信号")
        print(f"✅ 回测功能正常工作")
        print(f"✅ 数据库连接正常")
        
        print(f"\n💡 建议:")
        print(f"• 可以开始使用回调交易功能")
        print(f"• 建议从保守参数开始实盘测试")
        print(f"• 根据实际表现调整参数")
        
    else:
        print(f"⚠️ 策略仍需进一步优化")
        print(f"❌ 当前参数下无法产生交易信号")
        print(f"💡 可能的原因:")
        print(f"   • 市场数据特殊（周末、节假日）")
        print(f"   • 策略逻辑仍有问题")
        print(f"   • 需要更长的历史数据")
        
        print(f"\n🔧 建议的解决方案:")
        print(f"• 检查MT5数据连接")
        print(f"• 尝试不同的时间范围")
        print(f"• 进一步简化策略逻辑")
        print(f"• 添加更多调试信息")

if __name__ == '__main__':
    main()
