#!/usr/bin/env python3
"""
测试真实MT5数据获取
"""

from services.deep_learning_service import DeepLearningService
from datetime import datetime, timedelta

def test_real_mt5_data():
    """测试真实MT5数据获取"""
    print("🧪 测试真实MT5数据获取...")

    # 首先确保MT5连接
    print("1. 确保MT5连接...")
    from services.mt5_service import mt5_service

    if not mt5_service.get_connection_status().get('connected', False):
        print("   MT5未连接，尝试连接...")
        success = mt5_service.connect()
        if not success:
            print("   ❌ MT5连接失败")
            return False
        print("   ✅ MT5连接成功")
    else:
        print("   ✅ MT5已连接")

    print("2. 创建深度学习服务...")
    dl_service = DeepLearningService()
    print("3. 获取历史数据...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)

    result = dl_service._get_historical_data(
        symbol='XAUUSD',
        timeframe='H1',
        start_date=start_date.strftime('%Y-%m-%d'),
        end_date=end_date.strftime('%Y-%m-%d'),
        data_points=200
    )

    if result.get('success'):
        data = result['data']
        print(f'✅ 成功获取 {len(data)} 条真实MT5历史数据')
        if data:
            print(f'   时间范围: {data[0].get("timestamp")} 到 {data[-1].get("timestamp")}')
            print(f'   最新价格: {data[-1].get("close", 0):.5f}')
            print('   数据类型: 真实MT5数据 (非模拟)')
            
            # 显示前3条数据作为示例
            print('\n📊 数据示例 (前3条):')
            for i, item in enumerate(data[:3]):
                print(f'   {i+1}. 时间: {item.get("timestamp")}, 开盘: {item.get("open", 0):.5f}, 收盘: {item.get("close", 0):.5f}')
        
        return True
    else:
        print(f'❌ 获取数据失败: {result.get("error")}')
        return False

if __name__ == '__main__':
    test_real_mt5_data()
