#!/usr/bin/env python3
"""
测试GPU和深度学习模型训练功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gpu_detection():
    """测试GPU检测功能"""
    print("🔍 测试GPU检测功能...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 获取GPU状态
        gpu_status = deep_learning_service.get_gpu_status()
        
        print(f"📊 GPU状态:")
        print(f"   GPU可用: {'是' if gpu_status.get('gpu_available') else '否'}")
        
        if gpu_status.get('gpu_available'):
            print(f"   GPU名称: {gpu_status.get('gpu_name', 'N/A')}")
            print(f"   总内存: {gpu_status.get('memory_total', 0):.1f}GB")
            print(f"   已用内存: {gpu_status.get('memory_used', 0):.1f}GB")
            print(f"   可用内存: {gpu_status.get('memory_free', 0):.1f}GB")
            print(f"   CUDA版本: {gpu_status.get('cuda_version', 'N/A')}")
            print(f"   PyTorch版本: {gpu_status.get('pytorch_version', 'N/A')}")
        else:
            print(f"   错误信息: {gpu_status.get('error', 'GPU不可用')}")
        
        return gpu_status.get('gpu_available', False)
        
    except Exception as e:
        print(f"❌ GPU检测失败: {e}")
        return False

def test_pytorch_gpu():
    """测试PyTorch GPU功能"""
    print("\n🧪 测试PyTorch GPU功能...")
    
    try:
        import torch
        
        print(f"   PyTorch版本: {torch.__version__}")
        print(f"   CUDA可用: {'是' if torch.cuda.is_available() else '否'}")
        
        if torch.cuda.is_available():
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            print(f"   当前GPU: {torch.cuda.current_device()}")
            print(f"   GPU名称: {torch.cuda.get_device_name(0)}")
            
            # 测试GPU操作
            try:
                x = torch.randn(100, 100).cuda()
                y = torch.randn(100, 100).cuda()
                z = torch.mm(x, y)
                print(f"   GPU计算测试: ✅ 成功")
                
                # 检查内存使用
                memory_allocated = torch.cuda.memory_allocated(0) / 1024**3
                memory_reserved = torch.cuda.memory_reserved(0) / 1024**3
                print(f"   GPU内存使用: 已分配 {memory_allocated:.3f}GB, 已保留 {memory_reserved:.3f}GB")
                
                return True
                
            except Exception as e:
                print(f"   GPU计算测试: ❌ 失败 - {e}")
                return False
        else:
            print(f"   CUDA不可用，将使用CPU")
            return False
            
    except ImportError:
        print(f"   ❌ PyTorch未安装")
        return False
    except Exception as e:
        print(f"   ❌ PyTorch测试失败: {e}")
        return False

def test_training_progress_api():
    """测试训练进度API"""
    print("\n🔄 测试训练进度API...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 检查数据库连接
        import sqlite3
        conn = sqlite3.connect(deep_learning_service.db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('deep_learning_models', 'training_tasks')
        """)
        tables = cursor.fetchall()
        
        print(f"   数据库表: {[table[0] for table in tables]}")
        
        # 检查是否有训练任务
        cursor.execute("SELECT COUNT(*) FROM training_tasks")
        task_count = cursor.fetchone()[0]
        print(f"   训练任务数量: {task_count}")
        
        if task_count > 0:
            cursor.execute("SELECT id, status, progress FROM training_tasks ORDER BY created_at DESC LIMIT 5")
            recent_tasks = cursor.fetchall()
            print(f"   最近任务:")
            for task in recent_tasks:
                print(f"     任务ID: {task[0]}, 状态: {task[1]}, 进度: {task[2]}%")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 训练进度API测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🧠 测试模型创建...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 测试配置
        test_config = {
            'model_type': 'lstm',
            'input_size': 10,
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2,
            'output_size': 1
        }
        
        # 创建模型
        model = deep_learning_service._create_model(test_config, test_config['input_size'])
        
        if model:
            print(f"   ✅ 模型创建成功: {type(model).__name__}")
            
            # 计算参数数量
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            print(f"   总参数: {total_params:,}")
            print(f"   可训练参数: {trainable_params:,}")
            print(f"   设备: {next(model.parameters()).device}")
            
            return True
        else:
            print(f"   ❌ 模型创建失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 模型创建测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 深度学习模型训练功能测试")
    print("=" * 60)
    
    results = []
    
    # 1. 测试GPU检测
    gpu_available = test_gpu_detection()
    results.append(("GPU检测", gpu_available))
    
    # 2. 测试PyTorch GPU
    pytorch_gpu = test_pytorch_gpu()
    results.append(("PyTorch GPU", pytorch_gpu))
    
    # 3. 测试训练进度API
    progress_api = test_training_progress_api()
    results.append(("训练进度API", progress_api))
    
    # 4. 测试模型创建
    model_creation = test_model_creation()
    results.append(("模型创建", model_creation))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！深度学习功能正常")
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
        
        # 提供修复建议
        if not gpu_available:
            print("\n💡 GPU问题修复建议:")
            print("1. 确保安装了支持CUDA的PyTorch版本")
            print("2. 检查NVIDIA驱动程序是否正确安装")
            print("3. 验证CUDA工具包版本与PyTorch兼容")
            print("4. 重启系统后重试")

if __name__ == "__main__":
    main()
