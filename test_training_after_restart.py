#!/usr/bin/env python3
"""
应用重启后测试训练功能
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_simple_training():
    """测试简单训练"""
    
    print("🧪 测试简单训练功能")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return None
    
    # 使用最简单的配置
    config = {
        'model_name': f'restart_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 5,  # 很少的轮次
        'batch_size': 2,   # 很小的批次
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 5,  # 很短的序列
        'features': ['close'],  # 只用一个特征
        'early_stopping': False,  # 禁用早停
        'use_gpu': True,
        'save_checkpoints': False
    }
    
    print(f"📝 测试配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    try:
        print(f"\n🚀 启动测试训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return None

def monitor_simple_training(task_id, duration=60):
    """监控简单训练"""
    
    print(f"\n📊 监控简单训练 (任务: {task_id[:8]}..., 时长: {duration}秒)")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    start_time = time.time()
    progress_history = []
    
    print(f"🔄 开始监控:")
    
    for i in range(duration // 3):  # 每3秒检查一次
        try:
            current_time = time.time()
            elapsed = current_time - start_time
            
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    status = progress_data.get('status', 'unknown')
                    progress = progress_data.get('progress', 0)
                    epoch = progress_data.get('epoch', 0)
                    train_loss = progress_data.get('train_loss')
                    val_loss = progress_data.get('val_loss')
                    
                    progress_history.append({
                        'time': elapsed,
                        'status': status,
                        'progress': progress,
                        'epoch': epoch,
                        'train_loss': train_loss,
                        'val_loss': val_loss
                    })
                    
                    print(f"   [{elapsed:.0f}s] 状态: {status}, 进度: {progress}%, 轮次: {epoch}")
                    
                    if train_loss is not None and train_loss > 0:
                        print(f"   [{elapsed:.0f}s] 损失: 训练={train_loss:.4f}, 验证={val_loss:.4f if val_loss else 'N/A'}")
                    
                    # 检查训练状态
                    if status == 'completed':
                        print(f"   [{elapsed:.0f}s] 🎉 训练完成!")
                        return True
                    elif status == 'failed':
                        print(f"   [{elapsed:.0f}s] ❌ 训练失败!")
                        return False
                
                else:
                    print(f"   [{elapsed:.0f}s] ❌ API错误: {result.get('error')}")
            else:
                print(f"   [{elapsed:.0f}s] ❌ HTTP错误: {response.status_code}")
            
            time.sleep(3)
            
        except Exception as e:
            print(f"   [{elapsed:.0f}s] ❌ 监控异常: {e}")
            time.sleep(3)
    
    # 分析结果
    print(f"\n📈 训练分析:")
    
    if len(progress_history) >= 2:
        first = progress_history[0]
        last = progress_history[-1]
        
        print(f"   监控时长: {len(progress_history) * 3}秒")
        print(f"   状态变化: {first['status']} -> {last['status']}")
        print(f"   进度变化: {first['progress']}% -> {last['progress']}%")
        print(f"   轮次变化: {first['epoch']} -> {last['epoch']}")
        
        # 检查是否有进展
        has_progress = (last['progress'] > first['progress'] or 
                       last['epoch'] > first['epoch'] or
                       last['status'] in ['completed', 'failed'])
        
        if has_progress:
            print(f"   ✅ 训练有进展")
            return True
        else:
            print(f"   ❌ 训练无进展")
            return False
    else:
        print(f"   ⚠️ 监控数据不足")
        return False

def check_gpu_usage():
    """检查GPU使用情况"""
    
    print(f"\n🎮 检查GPU使用情况")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                gpu_status = result.get('gpu_status', {})
                
                print(f"📊 GPU状态:")
                print(f"   GPU可用: {gpu_status.get('gpu_available')}")
                print(f"   内存使用率: {gpu_status.get('memory_usage_percent', 0):.1f}%")
                print(f"   GPU使用率: {gpu_status.get('gpu_utilization', 0):.1f}%")
                print(f"   温度: {gpu_status.get('temperature', 0):.1f}°C")
                
                memory_usage = gpu_status.get('memory_usage_percent', 0)
                gpu_utilization = gpu_status.get('gpu_utilization', 0)
                
                if memory_usage > 0 or gpu_utilization > 0:
                    print(f"   ✅ GPU正在使用中")
                else:
                    print(f"   ⚠️ GPU未被使用")
                    
            else:
                print(f"❌ GPU状态API错误: {result.get('error')}")
        else:
            print(f"❌ GPU状态API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查GPU状态失败: {e}")

def main():
    """主函数"""
    
    print("🔄 应用重启后训练功能测试")
    print("=" * 80)
    
    print("📋 测试目标:")
    print("• 验证应用重启后训练功能是否正常")
    print("• 使用最简单的配置避免复杂问题")
    print("• 监控训练进度是否正常更新")
    print("• 检查GPU是否被正确使用")
    
    # 1. 检查GPU状态
    check_gpu_usage()
    
    # 2. 启动简单训练
    task_id = test_simple_training()
    
    if task_id:
        # 3. 等待训练开始
        print(f"\n⏳ 等待训练开始...")
        time.sleep(5)
        
        # 4. 监控训练进度
        training_ok = monitor_simple_training(task_id, duration=60)
        
        # 5. 再次检查GPU状态
        check_gpu_usage()
        
        print(f"\n📋 测试结果")
        print("=" * 80)
        
        if training_ok:
            print(f"🎉 应用重启后训练功能正常!")
            print(f"✅ 训练任务成功启动")
            print(f"✅ 训练进度正常更新")
            print(f"✅ 应用程序稳定运行")
            
            print(f"\n💡 建议:")
            print(f"• 现在可以使用正常的训练配置")
            print(f"• 建议从小配置开始，逐步增加复杂度")
            print(f"• 定期监控训练进度")
            
        else:
            print(f"⚠️ 训练功能可能仍有问题")
            print(f"💡 建议:")
            print(f"• 检查应用程序日志")
            print(f"• 尝试更简单的配置")
            print(f"• 考虑使用CPU模式")
            
    else:
        print(f"\n❌ 无法启动训练任务")
        print(f"💡 建议:")
        print(f"• 检查应用程序状态")
        print(f"• 查看错误日志")
        print(f"• 重新启动应用程序")
    
    print(f"\n🎯 下一步")
    print("=" * 80)
    
    print(f"📊 如果测试成功:")
    print(f"• 可以尝试正常的训练配置")
    print(f"• 逐步增加批次大小和序列长度")
    print(f"• 启用更多特征和检查点保存")
    
    print(f"\n⚠️ 如果测试失败:")
    print(f"• 检查MT5连接状态")
    print(f"• 检查数据库连接")
    print(f"• 查看详细的错误日志")
    print(f"• 考虑重新安装依赖")

if __name__ == '__main__':
    main()
