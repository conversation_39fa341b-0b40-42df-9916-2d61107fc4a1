#!/usr/bin/env python3
"""
测试HOLD预测逻辑修复
"""

import requests
import json

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_hold_prediction_logic():
    """测试HOLD预测逻辑"""
    
    print("⏸️ 测试HOLD预测逻辑修复")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print(f"❌ 获取模型失败: {response.status_code}")
            return False
        
        models_result = response.json()
        if not models_result.get('success'):
            print(f"❌ 获取模型失败: {models_result.get('error')}")
            return False
        
        models = models_result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print(f"❌ 没有找到训练完成的模型")
            return False
        
        test_model = completed_models[0]
        print(f"   使用模型: {test_model['name']} ({test_model['symbol']}, {test_model['timeframe']})")
        
        # 执行多次推理来获取不同类型的预测
        print("🔮 执行多次推理测试...")
        
        hold_predictions = []
        buy_predictions = []
        sell_predictions = []
        
        for i in range(5):  # 执行5次推理
            print(f"   第 {i+1} 次推理...")
            
            inference_data = {
                'model_id': test_model['id'],
                'symbol': test_model['symbol'],
                'timeframe': test_model['timeframe'],
                'inference_mode': 'realtime',
                'data_points': 50,
                'use_gpu': True,
                'show_confidence': True
            }
            
            response = session.post(
                'http://127.0.0.1:5000/api/deep-learning/inference',
                json=inference_data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    
                    for res in results:
                        if res['prediction'] == 'HOLD':
                            hold_predictions.append(res)
                        elif res['prediction'] == 'BUY':
                            buy_predictions.append(res)
                        elif res['prediction'] == 'SELL':
                            sell_predictions.append(res)
        
        print(f"\n📊 预测结果统计:")
        print(f"   HOLD预测: {len(hold_predictions)} 个")
        print(f"   BUY预测: {len(buy_predictions)} 个")
        print(f"   SELL预测: {len(sell_predictions)} 个")
        
        # 详细分析HOLD预测
        if hold_predictions:
            print(f"\n⏸️ HOLD预测详细分析:")
            
            valid_hold_count = 0
            
            for i, hold in enumerate(hold_predictions[:3], 1):  # 分析前3个HOLD预测
                current_price = hold.get('current_price', 0)
                price_target = hold.get('price_target', 0)
                confidence = hold.get('confidence', 0)
                analysis = hold.get('analysis', {})
                
                print(f"\n   HOLD预测 #{i}:")
                print(f"     当前价格: {current_price}")
                print(f"     目标价格: {price_target}")
                print(f"     价格差异: {abs(current_price - price_target):.5f}")
                print(f"     置信度: {confidence:.3f}")
                
                if analysis:
                    print(f"     价格变化: {analysis.get('price_change', 'N/A')}%")
                    print(f"     趋势: {analysis.get('trend', 'N/A')}")
                    print(f"     信号强度: {analysis.get('signal_strength', 'N/A')}")
                    print(f"     预测原因: {analysis.get('reason', 'N/A')}")
                
                # 验证HOLD预测的逻辑正确性
                price_diff = abs(current_price - price_target)
                price_diff_percent = (price_diff / current_price) * 100 if current_price > 0 else 0
                
                if price_diff_percent < 0.1:  # 价格差异小于0.1%认为是合理的
                    print(f"     ✅ HOLD预测逻辑正确 (价格差异: {price_diff_percent:.3f}%)")
                    valid_hold_count += 1
                else:
                    print(f"     ❌ HOLD预测逻辑异常 (价格差异: {price_diff_percent:.3f}%)")
            
            hold_accuracy = (valid_hold_count / min(len(hold_predictions), 3)) * 100
            print(f"\n   HOLD预测准确性: {hold_accuracy:.1f}% ({valid_hold_count}/{min(len(hold_predictions), 3)})")
            
            return hold_accuracy >= 80  # 80%以上认为修复成功
        else:
            print(f"\n⚠️ 没有获得HOLD预测，可能需要更多测试")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_prediction_display():
    """测试预测显示改进"""
    
    print(f"\n🖥️ 测试预测显示改进")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            # 检查显示改进的关键元素
            display_improvements = [
                ('predictionIcon', '预测图标显示'),
                ('targetDisplay', '目标价格显示逻辑'),
                ('tradingAdvice', '交易建议显示'),
                ('建议观望', 'HOLD预测特殊提示'),
                ('市场趋势不明显', 'HOLD预测说明文本'),
                ('analysis.reason', '预测原因显示')
            ]
            
            print(f"🔍 检查显示改进:")
            improvements_found = 0
            
            for element, description in display_improvements:
                if element in html_content:
                    print(f"   ✅ {description}: 已实现")
                    improvements_found += 1
                else:
                    print(f"   ❌ {description}: 未找到")
            
            improvement_rate = (improvements_found / len(display_improvements)) * 100
            print(f"\n   显示改进完成度: {improvement_rate:.1f}% ({improvements_found}/{len(display_improvements)})")
            
            return improvement_rate >= 70  # 70%以上认为改进成功
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试显示改进失败: {e}")
        return False

def main():
    """主函数"""
    
    print("⏸️ HOLD预测逻辑修复测试")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("1. HOLD预测目标价格逻辑")
    print("   • HOLD预测的目标价格现在等于当前价格")
    print("   • 不再使用随机变化的目标价格")
    print("   • 明确表示'观望'的含义")
    
    print("2. 预测逻辑改进")
    print("   • 明确的趋势判断阈值 (±1%)")
    print("   • 合理的置信度计算")
    print("   • 详细的预测原因说明")
    
    print("3. 显示界面改进")
    print("   • 添加预测图标 (📈📉⏸️)")
    print("   • HOLD预测特殊显示逻辑")
    print("   • 交易建议和原因说明")
    
    # 测试HOLD预测逻辑
    logic_ok = test_hold_prediction_logic()
    
    # 测试显示改进
    display_ok = test_prediction_display()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if logic_ok and display_ok:
        print(f"🎉 HOLD预测修复完全成功!")
        print(f"✅ 预测逻辑正确")
        print(f"✅ 显示改进完成")
        
        print(f"\n💡 修复效果:")
        print(f"⏸️ HOLD预测:")
        print(f"• 目标价格 = 当前价格 (不再有异常差异)")
        print(f"• 明确显示'建议观望'")
        print(f"• 提供详细的预测原因")
        print(f"• 使用⏸️图标表示暂停/观望")
        
        print(f"\n📈📉 BUY/SELL预测:")
        print(f"• 基于真实价格趋势分析")
        print(f"• 合理的目标价格计算")
        print(f"• 详细的技术分析信息")
        
        print(f"\n🎯 用户体验:")
        print(f"• 预测结果更加直观易懂")
        print(f"• HOLD预测不再误导用户")
        print(f"• 提供明确的交易建议")
        print(f"• 包含预测的技术分析依据")
        
    else:
        print(f"⚠️ 部分修复可能需要进一步完善")
        print(f"预测逻辑: {'✅' if logic_ok else '❌'}")
        print(f"显示改进: {'✅' if display_ok else '❌'}")
        
        print(f"\n🔧 故障排除:")
        if not logic_ok:
            print(f"• 检查智能推理算法的实现")
            print(f"• 验证HOLD预测的目标价格计算")
            print(f"• 确认趋势判断逻辑")
        if not display_ok:
            print(f"• 检查前端显示代码")
            print(f"• 确认HTML模板更新")
            print(f"• 验证JavaScript函数修改")
    
    print(f"\n🎯 使用建议")
    print("=" * 80)
    
    print(f"📊 理解预测结果:")
    print(f"• BUY (📈): 检测到上涨趋势，建议买入")
    print(f"• SELL (📉): 检测到下跌趋势，建议卖出")
    print(f"• HOLD (⏸️): 趋势不明显，建议观望等待")
    
    print(f"\n💡 交易建议:")
    print(f"• HOLD预测时不要强制交易")
    print(f"• 等待更明确的BUY/SELL信号")
    print(f"• 关注置信度和技术分析信息")
    print(f"• 结合多个时间框架进行判断")

if __name__ == '__main__':
    main()
