#!/usr/bin/env python3
"""
简化的推理测试，专门测试置信度和价格格式修复
"""

import requests
import json
import time

def test_simple_inference():
    """简化的推理测试"""
    
    print("🔧 简化推理测试 - 置信度和价格格式验证")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        print(f"✅ 使用模型: {test_model['name']}")
        
        # 简化的推理请求
        inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'data_points': 10,  # 减少数据点
            'inference_mode': 'realtime',
            'use_gpu': False,   # 不使用GPU避免问题
            'show_confidence': True
        }
        
        print(f"🔍 发送推理请求...")
        print(f"   模型ID: {test_model['id'][:8]}...")
        print(f"   交易品种: {test_model['symbol']}")
        print(f"   时间框架: {test_model['timeframe']}")
        
        start_time = time.time()
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=inference_data)
        end_time = time.time()
        
        print(f"   请求耗时: {end_time - start_time:.2f}秒")
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                predictions = result.get('results', [])
                print(f"   ✅ 推理成功")
                print(f"   📊 预测数量: {len(predictions)}")
                
                if len(predictions) > 0:
                    prediction = predictions[0]
                    
                    # 提取关键信息
                    pred_action = prediction.get('prediction', 'N/A')
                    confidence = prediction.get('confidence', 0)
                    price_target = prediction.get('price_target', 0)
                    current_price = prediction.get('current_price', 0)
                    
                    print(f"\n📈 推理结果详情:")
                    print(f"   预测方向: {pred_action}")
                    print(f"   置信度: {confidence}")
                    print(f"   置信度百分比: {confidence*100:.1f}%")
                    print(f"   当前价格: {current_price}")
                    print(f"   目标价格: {price_target}")
                    
                    # 验证置信度
                    confidence_valid = 0 <= confidence <= 1
                    confidence_reasonable = confidence <= 0.95
                    confidence_pct = confidence * 100
                    
                    print(f"\n✅ 置信度验证:")
                    print(f"   范围检查 (0-100%): {'✅通过' if confidence_valid else '❌失败'}")
                    print(f"   合理性检查 (≤95%): {'✅通过' if confidence_reasonable else '❌失败'}")
                    print(f"   数值: {confidence_pct:.1f}%")
                    
                    # 验证价格格式
                    price_format_ok = True
                    price_decimal_places = 0
                    
                    if isinstance(price_target, (int, float)):
                        price_str = str(price_target)
                        if '.' in price_str:
                            decimal_part = price_str.split('.')[1]
                            price_decimal_places = len(decimal_part)
                            
                            # XAUUSD应该是2位小数
                            if test_model['symbol'] == 'XAUUSD':
                                price_format_ok = price_decimal_places <= 2
                            else:
                                price_format_ok = price_decimal_places <= 5
                    
                    print(f"\n✅ 价格格式验证:")
                    print(f"   格式检查: {'✅通过' if price_format_ok else '❌失败'}")
                    print(f"   小数位数: {price_decimal_places}")
                    print(f"   期望小数位: {'≤2位 (XAUUSD)' if test_model['symbol'] == 'XAUUSD' else '≤5位'}")
                    
                    # 总体评估
                    all_checks_pass = confidence_valid and confidence_reasonable and price_format_ok
                    
                    print(f"\n🎯 修复效果评估:")
                    if all_checks_pass:
                        print(f"   🎉 所有检查通过！修复成功")
                        print(f"   ✅ 置信度: {confidence_pct:.1f}% (合理范围)")
                        print(f"   ✅ 价格格式: {price_target} ({price_decimal_places}位小数)")
                        return True
                    else:
                        print(f"   ❌ 部分检查失败")
                        if not confidence_valid:
                            print(f"      置信度超出范围: {confidence_pct:.1f}%")
                        if not confidence_reasonable:
                            print(f"      置信度过高: {confidence_pct:.1f}% > 95%")
                        if not price_format_ok:
                            print(f"      价格小数位过多: {price_decimal_places}位")
                        return False
                else:
                    print(f"   ⚠️ 没有预测结果")
                    return False
            else:
                print(f"   ❌ 推理失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text[:200]}")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    
    print("🔧 置信度和价格格式修复验证")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("1. 置信度计算: 修复超过100%的问题")
    print("2. 价格格式: 根据品种自动调整小数位")
    print("3. 验证函数: 添加范围检查和格式化")
    
    # 执行测试
    success = test_simple_inference()
    
    print(f"\n📊 最终结果")
    print("=" * 80)
    
    if success:
        print("🎉 修复验证成功!")
        print("✅ 置信度不再超过100%")
        print("✅ 价格格式规范化")
        print("✅ 所有验证通过")
        
        print(f"\n💡 修复效果:")
        print("• 置信度范围: 0-95% ✅")
        print("• 价格精度: 自动调整 ✅")
        print("• 显示效果: 清晰易读 ✅")
        
    else:
        print("❌ 修复验证失败")
        print("⚠️ 需要进一步调试")
        
        print(f"\n🔧 可能的问题:")
        print("• MT5连接状态不稳定")
        print("• 模型推理异常")
        print("• 验证逻辑需要调整")

if __name__ == '__main__':
    main()
