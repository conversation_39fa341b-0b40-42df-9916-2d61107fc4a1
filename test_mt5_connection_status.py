#!/usr/bin/env python3
"""
测试MT5连接状态显示和自动重连功能
"""

import requests
import json
import time

def test_mt5_connection_status_api():
    """测试MT5连接状态API"""
    
    print("🔧 测试MT5连接状态API")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 测试推理页面专用的连接状态API
        print(f"\n🔍 测试推理页面连接状态API")
        
        response = session.get('http://127.0.0.1:5000/api/mt5/inference-connection-status')
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ API响应成功")
            print(f"   📊 连接状态详情:")
            print(f"      连接状态: {'✅连接' if result.get('connected') else '❌断开'}")
            print(f"      推理就绪: {'✅就绪' if result.get('ready_for_inference') else '❌未就绪'}")
            
            if result.get('connected'):
                print(f"      交易品种: {result.get('symbols_count', 0)} 个")
                print(f"      价格数据: {'✅正常' if result.get('price_data_available') else '❌异常'}")
                print(f"      自动重连: {'✅启用' if result.get('reconnect_service_active') else '❌禁用'}")
                
                if result.get('auto_reconnected'):
                    print(f"      🔄 {result.get('reconnect_message', '已自动重连')}")
            else:
                print(f"      错误信息: {result.get('error', '未知错误')}")
            
            # 验证必要字段
            required_fields = ['success', 'connected', 'ready_for_inference']
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                print(f"   ❌ 缺少必要字段: {missing_fields}")
                return False
            else:
                print(f"   ✅ 所有必要字段完整")
                return True
        else:
            print(f"   ❌ API请求失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_mt5_force_reconnect():
    """测试MT5强制重连功能"""
    
    print(f"\n🔄 测试MT5强制重连功能")
    print("=" * 40)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 测试强制重连API
        print(f"🔍 发送强制重连请求...")
        
        start_time = time.time()
        response = session.post('http://127.0.0.1:5000/api/mt5/force-reconnect', 
                               headers={'Content-Type': 'application/json'})
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ 重连请求成功 (耗时: {end_time - start_time:.2f}秒)")
            
            if result.get('success'):
                print(f"   🎉 重连操作成功")
                print(f"   📝 消息: {result.get('message', '重连完成')}")
                
                # 等待一下再检查连接状态
                print(f"   ⏳ 等待2秒后检查连接状态...")
                time.sleep(2)
                
                # 检查重连后的状态
                status_response = session.get('http://127.0.0.1:5000/api/mt5/inference-connection-status')
                if status_response.status_code == 200:
                    status_result = status_response.json()
                    
                    if status_result.get('connected'):
                        print(f"   ✅ 重连后连接状态正常")
                        return True
                    else:
                        print(f"   ⚠️ 重连后连接状态仍异常: {status_result.get('error')}")
                        return False
                else:
                    print(f"   ❌ 无法检查重连后状态")
                    return False
            else:
                print(f"   ❌ 重连操作失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 重连请求失败: HTTP {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 重连测试异常: {e}")
        return False

def test_connection_status_monitoring():
    """测试连接状态监控"""
    
    print(f"\n📊 测试连接状态监控")
    print("=" * 30)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 连续检查几次状态，模拟监控
        check_count = 3
        results = []
        
        for i in range(check_count):
            print(f"🔍 第{i+1}次状态检查...")
            
            response = session.get('http://127.0.0.1:5000/api/mt5/inference-connection-status')
            
            if response.status_code == 200:
                result = response.json()
                
                status_info = {
                    'check_number': i + 1,
                    'connected': result.get('connected', False),
                    'ready_for_inference': result.get('ready_for_inference', False),
                    'symbols_count': result.get('symbols_count', 0),
                    'response_time': response.elapsed.total_seconds()
                }
                
                results.append(status_info)
                
                print(f"   连接: {'✅' if status_info['connected'] else '❌'} | "
                      f"就绪: {'✅' if status_info['ready_for_inference'] else '❌'} | "
                      f"品种: {status_info['symbols_count']} | "
                      f"响应: {status_info['response_time']:.3f}s")
            else:
                print(f"   ❌ 状态检查失败: HTTP {response.status_code}")
                results.append({
                    'check_number': i + 1,
                    'connected': False,
                    'error': f'HTTP {response.status_code}'
                })
            
            # 间隔2秒
            if i < check_count - 1:
                time.sleep(2)
        
        # 分析监控结果
        if len(results) > 0:
            connected_count = sum(1 for r in results if r.get('connected', False))
            success_rate = connected_count / len(results) * 100
            
            print(f"\n📈 监控结果分析:")
            print(f"   总检查次数: {len(results)}")
            print(f"   连接成功次数: {connected_count}")
            print(f"   连接成功率: {success_rate:.1f}%")
            
            if success_rate >= 66:  # 66%以上认为监控正常
                print(f"   ✅ 连接状态监控正常")
                return True
            else:
                print(f"   ⚠️ 连接状态不稳定")
                return False
        else:
            print(f"   ❌ 没有监控数据")
            return False
        
    except Exception as e:
        print(f"❌ 监控测试异常: {e}")
        return False

def show_feature_summary():
    """显示功能总结"""
    
    print(f"\n📋 MT5连接状态显示功能总结")
    print("=" * 60)
    
    print("🎨 前端界面功能:")
    print("1. ✅ 连接状态卡片显示")
    print("   • 实时连接状态指示 (绿色正常/红色异常)")
    print("   • 连接详情和错误信息显示")
    print("   • 自动重连状态提示")
    
    print("\n2. ✅ 详细状态信息")
    print("   • 可用交易品种数量")
    print("   • 价格数据获取状态")
    print("   • 自动重连服务状态")
    print("   • 推理就绪状态")
    
    print("\n3. ✅ 交互功能")
    print("   • 刷新状态按钮")
    print("   • 强制重连按钮")
    print("   • 自动状态监控 (30秒间隔)")
    
    print("\n🔧 后端API功能:")
    print("1. ✅ 推理专用连接状态API")
    print("   • /api/mt5/inference-connection-status")
    print("   • 自动重连检测和执行")
    print("   • 推理功能就绪性检查")
    
    print("\n2. ✅ 强制重连API")
    print("   • /api/mt5/force-reconnect")
    print("   • 手动触发重连操作")
    print("   • 重连结果反馈")
    
    print("\n🚀 技术特性:")
    print("• 自动断线检测和重连")
    print("• 实时状态监控 (30秒间隔)")
    print("• 详细的连接诊断信息")
    print("• 用户友好的状态指示")
    print("• 异常处理和错误提示")

def main():
    """主函数"""
    
    print("🔧 MT5连接状态显示和自动重连功能测试")
    print("=" * 80)
    
    # 显示功能总结
    show_feature_summary()
    
    # 测试连接状态API
    api_ok = test_mt5_connection_status_api()
    
    # 测试强制重连功能
    reconnect_ok = test_mt5_force_reconnect()
    
    # 测试状态监控
    monitoring_ok = test_connection_status_monitoring()
    
    print(f"\n📊 最终测试结果")
    print("=" * 80)
    
    if api_ok and reconnect_ok and monitoring_ok:
        print("🎉 MT5连接状态功能测试全部成功!")
        print("✅ 连接状态API正常工作")
        print("✅ 强制重连功能正常")
        print("✅ 状态监控功能稳定")
        
        print(f"\n💡 用户体验提升:")
        print("• 实时了解MT5连接状态")
        print("• 自动检测和处理连接断开")
        print("• 一键强制重连功能")
        print("• 详细的连接诊断信息")
        print("• 推理功能就绪性指示")
        
        print(f"\n🎯 功能价值:")
        print("• 提高系统可靠性")
        print("• 减少用户手动干预")
        print("• 提供透明的状态信息")
        print("• 增强用户信心")
        
    elif api_ok:
        print("🎉 连接状态API测试成功!")
        print("✅ 基本状态检查功能正常")
        print("⚠️ 重连或监控功能可能需要进一步检查")
        
    else:
        print("❌ 测试失败")
        print("⚠️ MT5连接状态功能需要进一步调试")
        
        print(f"\n🔧 故障排除:")
        print("• 检查MT5服务是否运行")
        print("• 验证API端点是否正确")
        print("• 确认前端JavaScript函数")
        print("• 测试网络连接状态")

if __name__ == '__main__':
    main()
