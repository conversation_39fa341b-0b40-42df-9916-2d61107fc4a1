#!/usr/bin/env python3
"""
测试交易状态持久化和回测功能
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_auto_trading_status_api():
    """测试自动交易状态API"""
    
    print("🔄 测试自动交易状态API")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 测试状态查询API
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        
        print(f"   API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 状态API正常工作")
                print(f"   活跃状态: {result.get('active', False)}")
                return True
            else:
                print(f"   ❌ 状态API返回失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 状态API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试状态API失败: {e}")
        return False

def test_backtest_functionality():
    """测试回测功能"""
    
    print(f"\n📊 测试回测功能")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 1. 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print(f"❌ 获取模型失败: {response.status_code}")
            return False
        
        models_result = response.json()
        if not models_result.get('success'):
            print(f"❌ 获取模型失败: {models_result.get('error')}")
            return False
        
        models = models_result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print(f"❌ 没有找到训练完成的模型")
            return False
        
        test_model = completed_models[0]
        print(f"   使用模型: {test_model['name']} ({test_model['symbol']}, {test_model['timeframe']})")
        
        # 2. 执行回测
        print("🔮 执行回测...")
        
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'start_date': '2024-07-01',
            'end_date': '2024-07-29',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.7
        }
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/backtest',
            json=backtest_data
        )
        
        print(f"   回测API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"   ✅ 回测成功完成")
                
                # 显示回测统计
                stats = result.get('statistics', {})
                print(f"\n   📊 回测统计:")
                print(f"     总收益: {stats.get('total_return', 0):.2f}%")
                print(f"     胜率: {stats.get('win_rate', 0):.1f}%")
                print(f"     总交易: {stats.get('total_trades', 0)}")
                print(f"     盈利交易: {stats.get('winning_trades', 0)}")
                print(f"     亏损交易: {stats.get('losing_trades', 0)}")
                print(f"     最大回撤: {stats.get('max_drawdown', 0):.2f}%")
                print(f"     盈利因子: {stats.get('profit_factor', 0):.2f}")
                
                # 显示部分交易记录
                trades = result.get('trades', [])
                print(f"\n   📈 交易记录 (前5笔):")
                for i, trade in enumerate(trades[:5], 1):
                    profit_symbol = "📈" if trade['profit'] > 0 else "📉" if trade['profit'] < 0 else "➖"
                    print(f"     {i}. {trade['prediction']} @ {trade['entry_price']:.5f} → {trade['exit_price']:.5f}")
                    print(f"        {profit_symbol} 盈亏: ${trade['profit']:.2f} | 余额: ${trade['balance']:.2f}")
                
                if len(trades) > 5:
                    print(f"     ... 还有 {len(trades) - 5} 笔交易")
                
                return True
            else:
                print(f"   ❌ 回测失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 回测API请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:300]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试回测功能失败: {e}")
        return False

def test_ui_improvements():
    """测试UI改进"""
    
    print(f"\n🖥️ 测试UI改进")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            # 检查新增的UI元素
            ui_elements = [
                ('startBacktestBtn', '回测按钮'),
                ('交易回测', '回测按钮文本'),
                ('backtestCard', '回测结果卡片'),
                ('backtestStats', '回测统计区域'),
                ('backtestResults', '回测详细结果区域'),
                ('restoreAutoTradingState', '状态恢复函数'),
                ('displayBacktestResults', '回测结果显示函数')
            ]
            
            print(f"🔍 检查UI改进:")
            improvements_found = 0
            
            for element, description in ui_elements:
                if element in html_content:
                    print(f"   ✅ {description}: 已添加")
                    improvements_found += 1
                else:
                    print(f"   ❌ {description}: 未找到")
            
            improvement_rate = (improvements_found / len(ui_elements)) * 100
            print(f"\n   UI改进完成度: {improvement_rate:.1f}% ({improvements_found}/{len(ui_elements)})")
            
            return improvement_rate >= 80  # 80%以上认为改进成功
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试UI改进失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 交易状态持久化和回测功能测试")
    print("=" * 80)
    
    print("📋 新增功能:")
    print("1. AI推理交易状态持久化")
    print("   • 页面刷新后自动恢复交易状态")
    print("   • 添加状态查询API")
    print("   • 自动重启交易循环")
    
    print("2. 深度学习模型推理回测功能")
    print("   • 基于历史数据的交易回测")
    print("   • 详细的盈亏统计分析")
    print("   • 完整的交易记录展示")
    print("   • 风险指标计算")
    
    # 测试自动交易状态API
    status_api_ok = test_auto_trading_status_api()
    
    # 测试回测功能
    backtest_ok = test_backtest_functionality()
    
    # 测试UI改进
    ui_ok = test_ui_improvements()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if status_api_ok and backtest_ok and ui_ok:
        print(f"🎉 所有新功能都成功!")
        print(f"✅ 交易状态API正常")
        print(f"✅ 回测功能正常")
        print(f"✅ UI改进完成")
        
        print(f"\n💡 功能亮点:")
        print(f"🔄 状态持久化:")
        print(f"• 页面刷新后自动检查交易状态")
        print(f"• 如有活跃交易会话会自动恢复")
        print(f"• 保持交易连续性，避免意外中断")
        
        print(f"\n📊 回测功能:")
        print(f"• 基于真实历史数据进行交易模拟")
        print(f"• 计算详细的盈亏统计指标")
        print(f"• 显示每笔交易的详细记录")
        print(f"• 评估AI模型的实际交易能力")
        
        print(f"\n🎯 使用方法:")
        print(f"• 在推理页面点击'交易回测'按钮")
        print(f"• 系统会自动使用选定的模型和参数")
        print(f"• 查看回测结果了解模型盈利能力")
        print(f"• 根据回测结果调整交易策略")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步完善")
        print(f"状态API: {'✅' if status_api_ok else '❌'}")
        print(f"回测功能: {'✅' if backtest_ok else '❌'}")
        print(f"UI改进: {'✅' if ui_ok else '❌'}")
        
        print(f"\n🔧 故障排除:")
        if not status_api_ok:
            print(f"• 检查状态查询API的实现")
            print(f"• 确认路由配置正确")
        if not backtest_ok:
            print(f"• 检查回测算法的实现")
            print(f"• 确认历史数据获取正常")
            print(f"• 验证统计计算逻辑")
        if not ui_ok:
            print(f"• 检查前端JavaScript代码")
            print(f"• 确认HTML模板更新")
    
    print(f"\n🎯 下一步建议")
    print("=" * 80)
    
    print(f"📊 回测分析:")
    print(f"• 关注总收益率和胜率指标")
    print(f"• 注意最大回撤控制风险")
    print(f"• 分析盈利因子评估策略质量")
    print(f"• 根据回测结果优化模型参数")
    
    print(f"\n🔍 实际验证:")
    print(f"• 访问推理页面测试回测功能")
    print(f"• 尝试刷新页面验证状态恢复")
    print(f"• 对比不同模型的回测表现")
    print(f"• 调整回测参数观察影响")

if __name__ == '__main__':
    main()
