#!/usr/bin/env python3
"""
测试推理模式逻辑修复
"""

import requests
import json

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_inference_mode_ui():
    """测试推理模式UI逻辑"""
    
    print("🖥️ 测试推理模式UI逻辑")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问模型推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            print(f"✅ 模型推理页面加载成功")
            
            # 检查推理模式相关的UI元素
            ui_elements = [
                'onchange="toggleTimeRangeVisibility()"',  # 推理模式变化事件
                'id="timeRangeSection"',                   # 时间范围区域ID
                'id="inferenceModeHelp"',                  # 推理模式帮助文本
                'toggleTimeRangeVisibility()',             # 切换函数
                'initializeDefaultTimeRange()',            # 初始化函数
            ]
            
            missing_elements = []
            for element in ui_elements:
                if element in html_content:
                    print(f"   ✅ 找到: {element}")
                else:
                    print(f"   ❌ 缺失: {element}")
                    missing_elements.append(element)
            
            # 检查推理模式选项
            inference_modes = [
                'value="single"',    # 单次推理
                'value="batch"',     # 批量推理
                'value="realtime"'   # 实时推理
            ]
            
            mode_found = 0
            for mode in inference_modes:
                if mode in html_content:
                    mode_found += 1
                    print(f"   ✅ 推理模式: {mode}")
            
            print(f"📊 推理模式选项: {mode_found}/{len(inference_modes)} 个可用")
            
            # 检查JavaScript逻辑
            js_logic_elements = [
                "inferenceMode === 'realtime'",           # 实时推理判断
                "timeRangeSection.style.display = 'none'", # 隐藏时间范围
                "timeRangeSection.style.display = 'block'", # 显示时间范围
                "实时推理：基于当前最新的市场数据",          # 实时推理说明
            ]
            
            js_found = 0
            for js_element in js_logic_elements:
                if js_element in html_content:
                    js_found += 1
                    print(f"   ✅ JS逻辑: {js_element[:30]}...")
            
            print(f"📊 JavaScript逻辑: {js_found}/{len(js_logic_elements)} 个实现")
            
            if len(missing_elements) == 0 and mode_found == 3 and js_found == len(js_logic_elements):
                print(f"✅ 推理模式UI逻辑完全正确")
                return True
            else:
                print(f"⚠️ 部分UI逻辑可能有问题")
                return False
                
        else:
            print(f"❌ 模型推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理模式UI失败: {e}")
        return False

def test_inference_request_logic():
    """测试推理请求逻辑"""
    
    print(f"\n📡 测试推理请求逻辑")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面检查JavaScript逻辑
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            # 检查推理请求的条件逻辑
            request_logic_elements = [
                "inferenceMode !== 'realtime'",           # 非实时推理条件
                "formData.start_date =",                  # 条件性添加开始日期
                "formData.end_date =",                    # 条件性添加结束日期
                "只有非实时推理模式才需要时间范围参数",      # 注释说明
            ]
            
            logic_found = 0
            for logic_element in request_logic_elements:
                if logic_element in html_content:
                    logic_found += 1
                    print(f"   ✅ 请求逻辑: {logic_element[:40]}...")
                else:
                    print(f"   ❌ 缺失逻辑: {logic_element[:40]}...")
            
            print(f"📊 请求逻辑: {logic_found}/{len(request_logic_elements)} 个实现")
            
            if logic_found == len(request_logic_elements):
                print(f"✅ 推理请求逻辑完全正确")
                return True
            else:
                print(f"⚠️ 推理请求逻辑可能有问题")
                return False
                
        else:
            print(f"❌ 无法检查推理请求逻辑")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理请求逻辑失败: {e}")
        return False

def test_inference_mode_descriptions():
    """测试推理模式说明"""
    
    print(f"\n📝 测试推理模式说明")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            # 检查各种推理模式的说明文本
            mode_descriptions = [
                "实时推理：基于当前最新的市场数据进行推理",
                "单次推理：对指定时间范围进行一次性推理", 
                "批量推理：对指定时间范围进行批量推理分析",
                "fas fa-broadcast-tower",  # 实时推理图标
                "fas fa-play-circle",      # 单次推理图标
                "fas fa-layer-group",      # 批量推理图标
            ]
            
            desc_found = 0
            for desc in mode_descriptions:
                if desc in html_content:
                    desc_found += 1
                    print(f"   ✅ 说明文本: {desc[:40]}...")
                else:
                    print(f"   ❌ 缺失说明: {desc[:40]}...")
            
            print(f"📊 模式说明: {desc_found}/{len(mode_descriptions)} 个实现")
            
            if desc_found >= len(mode_descriptions) - 1:  # 允许1个缺失
                print(f"✅ 推理模式说明基本完整")
                return True
            else:
                print(f"⚠️ 推理模式说明可能不完整")
                return False
                
        else:
            print(f"❌ 无法检查推理模式说明")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理模式说明失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 推理模式逻辑修复验证")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("• 实时推理模式不显示时间范围选择")
    print("• 单次/批量推理模式显示时间范围选择")
    print("• 推理请求根据模式条件性包含时间参数")
    print("• 为不同推理模式提供清晰的说明")
    
    print("\n💡 逻辑说明:")
    print("• 实时推理：基于当前最新市场数据，无需时间范围")
    print("• 单次推理：对指定历史时间范围进行一次推理")
    print("• 批量推理：对指定时间范围进行批量分析")
    
    # 测试推理模式UI逻辑
    ui_logic_ok = test_inference_mode_ui()
    
    # 测试推理请求逻辑
    request_logic_ok = test_inference_request_logic()
    
    # 测试推理模式说明
    descriptions_ok = test_inference_mode_descriptions()
    
    print(f"\n📋 修复验证结果")
    print("=" * 80)
    
    if ui_logic_ok and request_logic_ok and descriptions_ok:
        print(f"🎉 推理模式逻辑修复完全成功!")
        print(f"✅ UI显示逻辑正确")
        print(f"✅ 请求参数逻辑正确")
        print(f"✅ 模式说明完整")
        
        print(f"\n💡 修复成果:")
        print(f"• 实时推理模式隐藏时间范围选择，避免用户困惑")
        print(f"• 历史推理模式显示时间范围选择，支持精确控制")
        print(f"• 推理请求智能包含必要参数，提高API效率")
        print(f"• 提供清晰的模式说明，帮助用户理解")
        
        print(f"\n🎯 用户体验改善:")
        print(f"• 界面更加直观，减少不必要的配置项")
        print(f"• 推理模式选择更加明确")
        print(f"• 实时推理操作更加简洁")
        print(f"• 历史推理配置更加灵活")
        
    else:
        print(f"⚠️ 部分逻辑可能需要进一步调整")
        print(f"UI显示逻辑: {'✅' if ui_logic_ok else '❌'}")
        print(f"请求参数逻辑: {'✅' if request_logic_ok else '❌'}")
        print(f"模式说明: {'✅' if descriptions_ok else '❌'}")
    
    print(f"\n🔧 使用说明")
    print("=" * 80)
    
    print(f"📊 推理模式选择:")
    print(f"• 实时推理:")
    print(f"  - 基于当前最新的市场数据")
    print(f"  - 不需要选择时间范围")
    print(f"  - 适合实时交易决策")
    
    print(f"\n• 单次推理:")
    print(f"  - 对指定历史时间范围进行推理")
    print(f"  - 需要选择开始和结束日期")
    print(f"  - 适合历史数据分析")
    
    print(f"\n• 批量推理:")
    print(f"  - 对指定时间范围进行批量分析")
    print(f"  - 需要选择开始和结束日期")
    print(f"  - 适合大规模历史数据处理")
    
    print(f"\n💡 操作建议:")
    print(f"• 实时交易决策 → 选择'实时推理'")
    print(f"• 历史策略验证 → 选择'单次推理'或'批量推理'")
    print(f"• 根据推理模式，界面会自动调整显示内容")

if __name__ == '__main__':
    main()
