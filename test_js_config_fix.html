<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript配置修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 15px; }
        input, select { margin: 5px; padding: 5px; }
        .config-display { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔧 JavaScript配置修复测试</h1>
    <p class="info">测试目标: 验证 tradingConfig is not defined 错误已修复</p>

    <div class="test-section">
        <h2>📋 回测配置表单</h2>
        <div>
            <label>初始资金: <input type="number" id="backtestInitialBalance" value="10000"></label><br>
            <label>交易手数: <input type="number" id="backtestLotSize" value="0.01" step="0.01"></label><br>
            <label>止损点数: <input type="number" id="backtestStopLoss" value="30"></label><br>
            <label>止盈点数: <input type="number" id="backtestTakeProfit" value="60"></label><br>
            <label>最低置信度: <input type="number" id="backtestMinConfidence" value="0.5" step="0.01"></label><br>
            <label><input type="checkbox" id="backtestDynamicSL"> 动态止损</label><br>
            <label><input type="checkbox" id="enableTrailingStop" onchange="toggleTrailingStopConfig()"> 启用移动止损</label><br>
            <div id="trailingStopConfig" style="display: none; margin-left: 20px;">
                <label>移动止损距离: <input type="number" id="trailingStopDistance" value="20"></label><br>
                <label>移动止损步长: <input type="number" id="trailingStopStep" value="10"></label><br>
            </div>
            <label><input type="checkbox" id="enableCliffBrake"> 启用悬崖勒马</label><br>
        </div>
        <button onclick="testGetBacktestConfig()">测试获取回测配置</button>
        <button onclick="testValidateBacktestConfig()">测试验证回测配置</button>
    </div>

    <div class="test-section">
        <h2>🧪 配置测试结果</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>🔧 参数应用测试</h2>
        <button onclick="testApplyOptimizedParameters()">测试应用优化参数</button>
        <div id="applyResults"></div>
    </div>

    <script>
        // 复制修复后的函数
        function getBacktestConfig() {
            const trailingStopEnabled = document.getElementById('enableTrailingStop').checked;

            return {
                initial_balance: parseFloat(document.getElementById('backtestInitialBalance').value),
                lot_size: parseFloat(document.getElementById('backtestLotSize').value),
                stop_loss_pips: parseInt(document.getElementById('backtestStopLoss').value),
                take_profit_pips: parseInt(document.getElementById('backtestTakeProfit').value),
                min_confidence: parseFloat(document.getElementById('backtestMinConfidence').value),
                dynamic_sl: document.getElementById('backtestDynamicSL').checked,
                trailing_stop_enabled: trailingStopEnabled,
                trailing_stop_distance: trailingStopEnabled ? parseInt(document.getElementById('trailingStopDistance').value) : 0,
                trailing_stop_step: trailingStopEnabled ? parseInt(document.getElementById('trailingStopStep').value) : 0,
                cliff_brake_enabled: document.getElementById('enableCliffBrake').checked
            };
        }

        function validateBacktestConfig(config) {
            if (config.initial_balance < 1000 || config.initial_balance > 1000000) {
                return { valid: false, error: '初始资金必须在1,000-1,000,000之间' };
            }

            if (config.lot_size < 0.01 || config.lot_size > 10) {
                return { valid: false, error: '交易手数必须在0.01-10之间' };
            }

            if (config.min_confidence < 0.05 || config.min_confidence > 0.99) {
                return { valid: false, error: '最低置信度必须在0.05-0.99之间' };
            }

            return { valid: true };
        }

        // 模拟修复后的回测数据构建（关键修复点）
        function buildBacktestData() {
            const backtestConfig = getBacktestConfig();
            
            // 修复前的错误代码（已注释）：
            // cliff_brake_enabled: tradingConfig.enable_cliff_brake  // ❌ tradingConfig未定义
            
            // 修复后的正确代码：
            const backtestData = {
                model_id: 'test_model',
                symbol: 'XAUUSD',
                timeframe: 'H1',
                start_date: '2025-07-23',
                end_date: '2025-07-30',
                initial_balance: backtestConfig.initial_balance,
                lot_size: backtestConfig.lot_size,
                stop_loss_pips: backtestConfig.stop_loss_pips,
                take_profit_pips: backtestConfig.take_profit_pips,
                min_confidence: backtestConfig.min_confidence,
                cliff_brake_enabled: backtestConfig.cliff_brake_enabled,  // ✅ 使用正确的变量
                trailing_stop_enabled: backtestConfig.trailing_stop_enabled,
                trailing_stop_distance: backtestConfig.trailing_stop_distance,
                trailing_stop_step: backtestConfig.trailing_stop_step
            };

            return backtestData;
        }

        function applyOptimizedParameters(params) {
            console.log('📝 应用优化参数:', params);

            // 应用到回测配置
            document.getElementById('backtestLotSize').value = params.lot_size;
            document.getElementById('backtestStopLoss').value = params.stop_loss_pips;
            document.getElementById('backtestTakeProfit').value = params.take_profit_pips;
            document.getElementById('backtestMinConfidence').value = params.min_confidence;

            // 应用移动止损设置
            const trailingStopCheckbox = document.getElementById('enableTrailingStop');
            if (trailingStopCheckbox && params.trailing_stop_enabled !== undefined) {
                trailingStopCheckbox.checked = params.trailing_stop_enabled;

                if (params.trailing_stop_enabled) {
                    document.getElementById('trailingStopDistance').value = params.trailing_stop_distance || 20;
                    document.getElementById('trailingStopStep').value = params.trailing_stop_step || 10;
                }

                toggleTrailingStopConfig(); // 更新配置面板显示
            }

            // 应用悬崖勒马设置
            const cliffBrakeCheckbox = document.getElementById('enableCliffBrake');
            if (cliffBrakeCheckbox) {
                cliffBrakeCheckbox.checked = params.cliff_brake_enabled;
            }

            return '参数已应用到回测配置中！包含移动止损和悬崖勒马设置';
        }

        function toggleTrailingStopConfig() {
            const checkbox = document.getElementById('enableTrailingStop');
            const config = document.getElementById('trailingStopConfig');
            config.style.display = checkbox.checked ? 'block' : 'none';
        }

        // 测试函数
        function testGetBacktestConfig() {
            try {
                const config = getBacktestConfig();
                const results = document.getElementById('testResults');
                results.innerHTML = `
                    <h3 class="success">✅ getBacktestConfig() 测试通过</h3>
                    <div class="config-display">
                        <strong>回测配置:</strong><br>
                        ${JSON.stringify(config, null, 2)}
                    </div>
                `;
            } catch (error) {
                const results = document.getElementById('testResults');
                results.innerHTML = `
                    <h3 class="error">❌ getBacktestConfig() 测试失败</h3>
                    <div class="config-display">错误: ${error.message}</div>
                `;
            }
        }

        function testValidateBacktestConfig() {
            try {
                const config = getBacktestConfig();
                const validation = validateBacktestConfig(config);
                const results = document.getElementById('testResults');
                
                if (validation.valid) {
                    results.innerHTML += `
                        <h3 class="success">✅ validateBacktestConfig() 测试通过</h3>
                        <div class="config-display">配置验证成功</div>
                    `;
                } else {
                    results.innerHTML += `
                        <h3 class="error">❌ validateBacktestConfig() 测试失败</h3>
                        <div class="config-display">验证错误: ${validation.error}</div>
                    `;
                }
            } catch (error) {
                const results = document.getElementById('testResults');
                results.innerHTML += `
                    <h3 class="error">❌ validateBacktestConfig() 测试异常</h3>
                    <div class="config-display">错误: ${error.message}</div>
                `;
            }
        }

        function testApplyOptimizedParameters() {
            try {
                // 模拟优化参数
                const optimizedParams = {
                    lot_size: 0.02,
                    stop_loss_pips: 25,
                    take_profit_pips: 75,
                    min_confidence: 0.6,
                    cliff_brake_enabled: true,
                    trailing_stop_enabled: true,
                    trailing_stop_distance: 30,
                    trailing_stop_step: 15
                };

                const message = applyOptimizedParameters(optimizedParams);
                const results = document.getElementById('applyResults');
                results.innerHTML = `
                    <h3 class="success">✅ applyOptimizedParameters() 测试通过</h3>
                    <div class="config-display">
                        <strong>应用结果:</strong> ${message}<br>
                        <strong>应用的参数:</strong><br>
                        ${JSON.stringify(optimizedParams, null, 2)}
                    </div>
                `;
            } catch (error) {
                const results = document.getElementById('applyResults');
                results.innerHTML = `
                    <h3 class="error">❌ applyOptimizedParameters() 测试失败</h3>
                    <div class="config-display">错误: ${error.message}</div>
                `;
            }
        }

        function testBuildBacktestData() {
            try {
                const backtestData = buildBacktestData();
                const results = document.getElementById('testResults');
                results.innerHTML += `
                    <h3 class="success">✅ buildBacktestData() 测试通过</h3>
                    <div class="config-display">
                        <strong>回测数据:</strong><br>
                        ${JSON.stringify(backtestData, null, 2)}
                    </div>
                    <p class="success">🎉 关键修复验证: cliff_brake_enabled 使用了正确的 backtestConfig 变量</p>
                `;
            } catch (error) {
                const results = document.getElementById('testResults');
                results.innerHTML += `
                    <h3 class="error">❌ buildBacktestData() 测试失败</h3>
                    <div class="config-display">错误: ${error.message}</div>
                `;
            }
        }

        // 页面加载完成后自动运行测试
        window.onload = function() {
            console.log('🔧 开始JavaScript配置修复测试');
            
            // 自动运行关键测试
            setTimeout(() => {
                testGetBacktestConfig();
                setTimeout(() => {
                    testBuildBacktestData();
                }, 500);
            }, 100);
        };
    </script>

    <div class="test-section">
        <h2>🎯 修复总结</h2>
        <div class="info">
            <h3>❌ 修复前的问题:</h3>
            <code>cliff_brake_enabled: tradingConfig.enable_cliff_brake</code>
            <p>错误: tradingConfig is not defined</p>
            
            <h3>✅ 修复后的解决方案:</h3>
            <code>cliff_brake_enabled: backtestConfig.cliff_brake_enabled</code>
            <p>使用正确定义的 backtestConfig 变量</p>
            
            <h3>🔧 额外改进:</h3>
            <ul>
                <li>添加了完整的移动止损参数</li>
                <li>确保所有回测参数都正确传递</li>
                <li>统一了参数命名规范</li>
            </ul>
        </div>
    </div>

    <button onclick="testBuildBacktestData()" style="background: #007bff; color: white; padding: 15px 30px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer;">
        🧪 测试关键修复点
    </button>
</body>
</html>
