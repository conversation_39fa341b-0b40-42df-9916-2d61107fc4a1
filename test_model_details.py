#!/usr/bin/env python3
"""
测试模型详情信息显示
"""

import requests
import json

def test_model_details():
    """测试模型详情获取"""
    
    print("🔍 测试模型详情信息显示")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取模型列表
        print("📋 获取模型列表...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        if not result.get('success'):
            print("❌ 模型列表API返回失败")
            return False
        
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        print(f"总模型数: {len(models)}")
        print(f"完成训练的模型: {len(completed_models)}")
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        # 测试第一个模型的详情
        test_model = completed_models[0]
        model_id = test_model['id']
        
        print(f"\n🔍 测试模型详情: {test_model['name']} ({model_id[:8]}...)")
        
        # 获取模型详情
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/models/{model_id}')
        
        if response.status_code != 200:
            print(f"❌ 获取模型详情失败: HTTP {response.status_code}")
            return False
        
        result = response.json()
        
        if not result.get('success'):
            print(f"❌ 模型详情API返回失败: {result.get('error')}")
            return False
        
        model = result.get('model', {})
        
        print("✅ 模型详情获取成功")
        print("=" * 60)
        
        # 检查基本信息
        print("📊 基本信息:")
        print(f"  模型名称: {model.get('name', 'N/A')}")
        print(f"  模型类型: {model.get('model_type', 'N/A')}")
        print(f"  交易品种: {model.get('symbol', 'N/A')}")
        print(f"  时间框架: {model.get('timeframe', 'N/A')}")
        print(f"  状态: {model.get('status', 'N/A')}")
        print(f"  模型大小: {model.get('model_size_mb', 'N/A')} MB")
        print(f"  创建时间: {model.get('created_at', 'N/A')}")
        print(f"  完成时间: {model.get('completed_at', 'N/A')}")
        
        # 检查性能指标
        print(f"\n📈 性能指标:")
        performance = model.get('performance', {})
        print(f"  准确率: {performance.get('accuracy', 'N/A')}")
        print(f"  精确率: {performance.get('precision', 'N/A')}")
        print(f"  召回率: {performance.get('recall', 'N/A')}")
        print(f"  F1分数: {performance.get('f1_score', 'N/A')}")
        print(f"  训练损失: {performance.get('final_train_loss', 'N/A')}")
        print(f"  验证损失: {performance.get('final_val_loss', 'N/A')}")
        
        # 检查训练数据信息
        print(f"\n📊 训练数据信息:")
        data_info = model.get('data_info', {})
        print(f"  数据时间范围: {data_info.get('start_date', 'N/A')} 至 {data_info.get('end_date', 'N/A')}")
        print(f"  总样本数: {data_info.get('total_samples', 'N/A')}")
        print(f"  训练样本: {data_info.get('training_samples', 'N/A')}")
        print(f"  验证样本: {data_info.get('validation_samples', 'N/A')}")
        print(f"  使用特征: {data_info.get('features_used', 'N/A')}")
        print(f"  数据质量: {data_info.get('data_quality', 'N/A')}")
        
        # 检查模型配置
        print(f"\n⚙️ 模型配置:")
        config = model.get('config', {})
        print(f"  序列长度: {config.get('sequence_length', 'N/A')}")
        print(f"  隐藏层大小: {config.get('hidden_size', 'N/A')}")
        print(f"  网络层数: {config.get('num_layers', 'N/A')}")
        print(f"  批次大小: {config.get('batch_size', 'N/A')}")
        print(f"  学习率: {config.get('learning_rate', 'N/A')}")
        print(f"  训练轮次: {config.get('epochs', 'N/A')}")
        print(f"  验证比例: {config.get('validation_split', 'N/A')}")
        print(f"  早停耐心: {config.get('patience', 'N/A')}")
        
        # 评估信息完整性
        print(f"\n📋 信息完整性评估:")
        
        missing_info = []
        
        # 检查基本信息
        if not model.get('name'):
            missing_info.append("模型名称")
        if not model.get('model_type'):
            missing_info.append("模型类型")
        if not model.get('symbol'):
            missing_info.append("交易品种")
        if not model.get('timeframe'):
            missing_info.append("时间框架")
        
        # 检查性能指标
        if not performance.get('accuracy'):
            missing_info.append("准确率")
        if not performance.get('precision'):
            missing_info.append("精确率")
        if not performance.get('recall'):
            missing_info.append("召回率")
        if not performance.get('f1_score'):
            missing_info.append("F1分数")
        
        # 检查数据信息
        if not data_info.get('start_date'):
            missing_info.append("开始日期")
        if not data_info.get('end_date'):
            missing_info.append("结束日期")
        if not data_info.get('total_samples'):
            missing_info.append("总样本数")
        if not data_info.get('training_samples'):
            missing_info.append("训练样本数")
        if not data_info.get('validation_samples'):
            missing_info.append("验证样本数")
        if not data_info.get('features_used'):
            missing_info.append("使用特征")
        
        # 检查配置信息
        if not config.get('sequence_length'):
            missing_info.append("序列长度")
        if not config.get('hidden_size'):
            missing_info.append("隐藏层大小")
        if not config.get('num_layers'):
            missing_info.append("网络层数")
        if not config.get('batch_size'):
            missing_info.append("批次大小")
        if not config.get('learning_rate'):
            missing_info.append("学习率")
        if not config.get('epochs'):
            missing_info.append("训练轮次")
        
        if missing_info:
            print(f"❌ 缺失信息: {', '.join(missing_info)}")
            print(f"信息完整度: {((len(missing_info) / 20) * 100):.1f}% 缺失")
            return False
        else:
            print("✅ 所有信息完整")
            print("信息完整度: 100%")
            return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 AI推理训练模型详情信息测试")
    print("=" * 80)
    
    success = test_model_details()
    
    print(f"\n📊 测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 测试成功!")
        print("✅ 模型详情信息显示完整")
        print("✅ 所有必要信息都已正确获取和显示")
        
        print(f"\n💡 功能说明:")
        print("• 模型详情页面现在显示完整的训练信息")
        print("• 包括性能指标、数据信息、模型配置等")
        print("• 用户可以全面了解模型的训练情况")
        
    else:
        print("❌ 测试失败")
        print("⚠️ 模型详情信息仍有缺失")
        
        print(f"\n🔧 可能的解决方案:")
        print("• 检查训练过程中的数据信息保存逻辑")
        print("• 验证模型配置信息的存储")
        print("• 确认性能指标的计算和保存")
        print("• 重新训练模型以获取完整信息")

if __name__ == '__main__':
    main()
