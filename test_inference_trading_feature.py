#!/usr/bin/env python3
"""
测试推理交易功能
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_inference_trading_ui():
    """测试推理交易UI"""
    
    print("🖥️ 测试推理交易UI")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            print(f"✅ 推理页面加载成功")
            
            # 检查推理交易区域的UI元素
            trading_ui_elements = [
                'id="tradingSection"',                    # 交易区域
                'AI推理交易',                             # 标题
                'id="mt5ConnectionStatus"',               # MT5连接状态
                'id="tradingLotSize"',                    # 交易手数
                'id="maxPositions"',                      # 最大持仓数
                'id="stopLossPips"',                      # 止损点数
                'id="takeProfitPips"',                    # 止盈点数
                'id="minConfidence"',                     # 最低置信度
                'id="inferenceInterval"',                 # 推理间隔
                'id="enableAutoTrading"',                 # 启用自动交易
                'id="enableTrailingStop"',                # 移动止损
                'id="enableNewsFilter"',                  # 新闻过滤
                'id="currentBid"',                        # 当前买价
                'id="currentAsk"',                        # 当前卖价
                'id="currentSpread"',                     # 当前点差
                'id="latestInferenceResult"',             # 最新推理结果
                'id="todayTrades"',                       # 今日交易
                'id="currentPositions"',                  # 当前持仓
                'startAutoTrading()',                     # 开始交易函数
                'stopAutoTrading()',                      # 停止交易函数
                'closeAllPositions()',                    # 平仓函数
                'checkMT5Connection()',                   # 检查连接函数
            ]
            
            missing_elements = []
            for element in trading_ui_elements:
                if element in html_content:
                    print(f"   ✅ 找到: {element}")
                else:
                    print(f"   ❌ 缺失: {element}")
                    missing_elements.append(element)
            
            print(f"📊 UI元素: {len(trading_ui_elements) - len(missing_elements)}/{len(trading_ui_elements)} 个存在")
            
            if len(missing_elements) == 0:
                print(f"✅ 推理交易UI完全正确")
                return True
            else:
                print(f"⚠️ 缺少 {len(missing_elements)} 个UI元素")
                return False
                
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理交易UI失败: {e}")
        return False

def test_trading_api_endpoints():
    """测试交易API端点"""
    
    print(f"\n📡 测试交易API端点")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    # 测试的API端点
    api_endpoints = [
        {
            'name': 'MT5连接状态',
            'method': 'GET',
            'url': '/api/mt5/connection-status',
            'expected_fields': ['success', 'connected']
        },
        {
            'name': '市场数据',
            'method': 'GET', 
            'url': '/api/mt5/market-data/XAUUSD',
            'expected_fields': ['success', 'market_data']
        },
        {
            'name': '交易统计',
            'method': 'GET',
            'url': '/api/deep-learning/trading-statistics',
            'expected_fields': ['success', 'statistics']
        }
    ]
    
    api_results = []
    
    for endpoint in api_endpoints:
        try:
            print(f"\n🔍 测试: {endpoint['name']}")
            
            if endpoint['method'] == 'GET':
                response = session.get(f"http://127.0.0.1:5000{endpoint['url']}")
            else:
                response = session.post(f"http://127.0.0.1:5000{endpoint['url']}")
            
            print(f"   HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   JSON解析: ✅")
                    
                    # 检查预期字段
                    missing_fields = []
                    for field in endpoint['expected_fields']:
                        if field in result:
                            print(f"   字段 {field}: ✅")
                        else:
                            print(f"   字段 {field}: ❌")
                            missing_fields.append(field)
                    
                    if len(missing_fields) == 0:
                        print(f"   结果: ✅ API正常")
                        api_results.append(True)
                    else:
                        print(f"   结果: ⚠️ 缺少字段")
                        api_results.append(False)
                        
                except json.JSONDecodeError:
                    print(f"   JSON解析: ❌")
                    print(f"   结果: ❌ 响应不是有效JSON")
                    api_results.append(False)
            else:
                print(f"   结果: ❌ HTTP错误")
                api_results.append(False)
                
        except Exception as e:
            print(f"   异常: {e}")
            print(f"   结果: ❌ 请求失败")
            api_results.append(False)
    
    success_count = sum(api_results)
    total_count = len(api_results)
    
    print(f"\n📊 API测试结果: {success_count}/{total_count} 个端点正常")
    
    return success_count == total_count

def test_trading_configuration():
    """测试交易配置验证"""
    
    print(f"\n⚙️ 测试交易配置验证")
    print("=" * 60)
    
    # 测试配置参数
    test_configs = [
        {
            'name': '正常配置',
            'config': {
                'lot_size': 0.1,
                'max_positions': 3,
                'stop_loss_pips': 50,
                'take_profit_pips': 100,
                'min_confidence': 0.75,
                'inference_interval': 60,
                'trading_start_time': '09:00',
                'trading_end_time': '17:00',
                'enable_trailing_stop': False,
                'enable_news_filter': True
            },
            'should_pass': True
        },
        {
            'name': '手数过大',
            'config': {
                'lot_size': 15.0,  # 超过限制
                'max_positions': 3,
                'min_confidence': 0.75
            },
            'should_pass': False
        },
        {
            'name': '置信度过低',
            'config': {
                'lot_size': 0.1,
                'max_positions': 3,
                'min_confidence': 0.3  # 低于最低要求
            },
            'should_pass': False
        }
    ]
    
    validation_results = []
    
    for test_config in test_configs:
        print(f"\n🧪 测试配置: {test_config['name']}")
        
        config = test_config['config']
        should_pass = test_config['should_pass']
        
        # 模拟前端验证逻辑
        validation_passed = True
        validation_errors = []
        
        # 检查手数
        if config.get('lot_size', 0) <= 0 or config.get('lot_size', 0) > 10:
            validation_passed = False
            validation_errors.append('交易手数超出范围')
        
        # 检查最大持仓数
        if config.get('max_positions', 0) <= 0 or config.get('max_positions', 0) > 10:
            validation_passed = False
            validation_errors.append('最大持仓数超出范围')
        
        # 检查置信度
        if config.get('min_confidence', 0) < 0.5 or config.get('min_confidence', 0) > 0.99:
            validation_passed = False
            validation_errors.append('最低置信度超出范围')
        
        print(f"   验证结果: {'✅ 通过' if validation_passed else '❌ 失败'}")
        if validation_errors:
            for error in validation_errors:
                print(f"   错误: {error}")
        
        # 检查是否符合预期
        if validation_passed == should_pass:
            print(f"   预期结果: ✅ 符合")
            validation_results.append(True)
        else:
            print(f"   预期结果: ❌ 不符合 (期望{'通过' if should_pass else '失败'})")
            validation_results.append(False)
    
    success_count = sum(validation_results)
    total_count = len(validation_results)
    
    print(f"\n📊 配置验证测试: {success_count}/{total_count} 个测试通过")
    
    return success_count == total_count

def main():
    """主函数"""
    
    print("🤖 推理交易功能测试")
    print("=" * 80)
    
    print("📋 功能特性:")
    print("• AI推理交易区域界面")
    print("• 完整的交易参数配置")
    print("• 实时MT5市场数据显示")
    print("• 自动交易启停控制")
    print("• 风险管理参数设置")
    print("• 交易统计和持仓监控")
    print("• 基于推理结果的智能交易")
    
    # 测试推理交易UI
    ui_ok = test_inference_trading_ui()
    
    # 测试交易API端点
    api_ok = test_trading_api_endpoints()
    
    # 测试交易配置验证
    config_ok = test_trading_configuration()
    
    print(f"\n📋 推理交易功能测试结果")
    print("=" * 80)
    
    if ui_ok and api_ok and config_ok:
        print(f"🎉 推理交易功能开发完全成功!")
        print(f"✅ 交易界面完整")
        print(f"✅ API端点正常")
        print(f"✅ 配置验证正确")
        
        print(f"\n💡 功能亮点:")
        print(f"• 完整的AI推理交易界面")
        print(f"• 实时MT5市场数据集成")
        print(f"• 智能交易参数配置")
        print(f"• 风险管理和资金管理")
        print(f"• 自动化交易执行")
        print(f"• 实时交易统计监控")
        
        print(f"\n🎯 使用流程:")
        print(f"1. 选择训练好的深度学习模型")
        print(f"2. 执行推理获得预测结果")
        print(f"3. 配置交易参数和风险管理")
        print(f"4. 启用自动交易功能")
        print(f"5. 系统基于推理结果自动执行交易")
        print(f"6. 实时监控交易状态和盈亏")
        
    else:
        print(f"⚠️ 部分功能需要进一步完善")
        print(f"交易界面: {'✅' if ui_ok else '❌'}")
        print(f"API端点: {'✅' if api_ok else '❌'}")
        print(f"配置验证: {'✅' if config_ok else '❌'}")
    
    print(f"\n🔧 技术实现")
    print("=" * 80)
    
    print(f"📊 前端功能:")
    print(f"• 推理交易区域UI (HTML + Bootstrap)")
    print(f"• 交易参数配置表单")
    print(f"• 实时数据显示和更新")
    print(f"• 自动交易控制逻辑")
    print(f"• MT5连接状态监控")
    
    print(f"\n🔧 后端API:")
    print(f"• /api/deep-learning/auto-trading/start - 启动自动交易")
    print(f"• /api/deep-learning/auto-trading/stop - 停止自动交易")
    print(f"• /api/deep-learning/execute-trade - 执行AI交易")
    print(f"• /api/deep-learning/trading-statistics - 获取交易统计")
    print(f"• /api/deep-learning/close-all-positions - 平仓所有持仓")
    print(f"• /api/mt5/market-data/<symbol> - 获取实时市场数据")
    
    print(f"\n💾 数据存储:")
    print(f"• ai_trading_sessions - AI交易会话表")
    print(f"• ai_trades - AI交易记录表")
    print(f"• 完整的交易历史和统计数据")

if __name__ == '__main__':
    main()
