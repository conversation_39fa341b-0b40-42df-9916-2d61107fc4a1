#!/usr/bin/env python3
"""
修复AI推理模型训练进度卡住问题
"""

import sqlite3
import requests
import time
import json
from datetime import datetime, timed<PERSON><PERSON>

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查训练任务表
        cursor.execute("SELECT COUNT(*) FROM training_tasks")
        total_tasks = cursor.fetchone()[0]
        print(f"📊 训练任务总数: {total_tasks}")
        
        # 检查活跃任务
        cursor.execute("""
            SELECT id, name, status, progress, current_epoch, total_epochs, updated_at
            FROM training_tasks 
            WHERE status IN ('running', 'pending')
            ORDER BY updated_at DESC
        """)
        
        active_tasks = cursor.fetchall()
        
        if active_tasks:
            print(f"🔄 发现 {len(active_tasks)} 个活跃任务:")
            
            stuck_tasks = []
            for task in active_tasks:
                task_id, name, status, progress, epoch, total_epochs, updated_at = task
                print(f"\n📋 任务: {task_id[:8]}...")
                print(f"   名称: {name}")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {epoch}/{total_epochs}")
                print(f"   更新: {updated_at}")
                
                # 检查是否卡住
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00').replace('+00:00', ''))
                        now = datetime.now()
                        time_diff = now - last_update
                        
                        if time_diff > timedelta(minutes=5):
                            print(f"   ⚠️ 可能卡住 (已 {time_diff} 没有更新)")
                            stuck_tasks.append(task_id)
                        else:
                            print(f"   ✅ 正常 (最后更新: {time_diff} 前)")
                    except Exception as e:
                        print(f"   ❓ 时间解析错误: {e}")
                        stuck_tasks.append(task_id)
            
            conn.close()
            return active_tasks, stuck_tasks
        else:
            print("✅ 没有活跃的训练任务")
            conn.close()
            return [], []
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return [], []

def check_api_status():
    """检查API状态"""
    print("\n🌐 检查API状态...")
    
    try:
        # 检查应用程序是否运行
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ 应用程序API正常运行")
            return True
        else:
            print(f"⚠️ API响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到应用程序 (可能未启动)")
        return False
    except Exception as e:
        print(f"❌ API检查失败: {e}")
        return False

def restart_stuck_tasks(stuck_task_ids):
    """重启卡住的任务"""
    if not stuck_task_ids:
        print("✅ 没有卡住的任务需要重启")
        return
    
    print(f"\n🔄 重启 {len(stuck_task_ids)} 个卡住的任务...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        for task_id in stuck_task_ids:
            print(f"🔄 重启任务: {task_id[:8]}...")
            
            # 重置任务状态
            cursor.execute("""
                UPDATE training_tasks
                SET status = 'pending',
                    progress = 0,
                    current_epoch = 0,
                    train_loss = 0,
                    val_loss = 0,
                    updated_at = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), task_id))
            
            print(f"   ✅ 任务已重置为pending状态")
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已重启 {len(stuck_task_ids)} 个任务")
        
    except Exception as e:
        print(f"❌ 重启任务失败: {e}")

def trigger_training_restart():
    """通过API触发训练重启"""
    print("\n🚀 通过API触发训练重启...")
    
    try:
        # 获取pending状态的任务
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name FROM training_tasks 
            WHERE status = 'pending'
            ORDER BY updated_at DESC
            LIMIT 1
        """)
        
        pending_task = cursor.fetchone()
        conn.close()
        
        if pending_task:
            task_id, task_name = pending_task
            print(f"📋 找到待重启任务: {task_name} ({task_id[:8]}...)")
            
            # 这里可以添加API调用来重启训练
            # 由于训练通常是通过前端触发的，我们只是确保任务状态正确
            print("✅ 任务已准备好重启，请在前端页面重新开始训练")
            return True
        else:
            print("❌ 没有找到待重启的任务")
            return False
            
    except Exception as e:
        print(f"❌ 触发重启失败: {e}")
        return False

def monitor_training_progress(duration=60):
    """监控训练进度变化"""
    print(f"\n⏱️ 监控训练进度变化 ({duration}秒)...")
    
    start_time = time.time()
    last_progress = {}
    
    while time.time() - start_time < duration:
        try:
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, progress, current_epoch, updated_at
                FROM training_tasks 
                WHERE status = 'running'
            """)
            
            current_tasks = cursor.fetchall()
            conn.close()
            
            if current_tasks:
                for task_id, progress, epoch, updated_at in current_tasks:
                    task_key = task_id[:8]
                    
                    if task_key in last_progress:
                        if (progress != last_progress[task_key]['progress'] or 
                            epoch != last_progress[task_key]['epoch']):
                            print(f"📈 {task_key}... 进度更新: {progress}% (轮次 {epoch})")
                    
                    last_progress[task_key] = {
                        'progress': progress,
                        'epoch': epoch,
                        'updated_at': updated_at
                    }
            else:
                print("📊 没有正在运行的训练任务")
                break
                
        except Exception as e:
            print(f"❌ 监控失败: {e}")
            break
        
        time.sleep(10)  # 每10秒检查一次
    
    print("⏱️ 监控完成")

def main():
    """主函数"""
    print("🔧 AI推理模型训练进度修复工具")
    print("=" * 80)
    
    # 1. 检查数据库状态
    active_tasks, stuck_tasks = check_database_status()
    
    # 2. 检查API状态
    api_running = check_api_status()
    
    if not api_running:
        print("\n💡 建议:")
        print("1. 启动应用程序: python app.py")
        print("2. 等待应用程序完全启动")
        print("3. 重新运行此脚本")
        return
    
    # 3. 处理卡住的任务
    if stuck_tasks:
        print(f"\n⚠️ 发现 {len(stuck_tasks)} 个卡住的任务")
        restart_stuck_tasks(stuck_tasks)
        
        # 4. 触发重启
        trigger_training_restart()
        
        # 5. 监控进度
        print("\n💡 请在前端页面重新开始训练，然后观察进度变化")
        monitor_training_progress(60)
        
    elif active_tasks:
        print(f"\n✅ 发现 {len(active_tasks)} 个正常运行的任务")
        print("📊 监控进度变化...")
        monitor_training_progress(60)
        
    else:
        print("\n✅ 当前没有活跃的训练任务")
        print("💡 可以在前端页面开始新的训练")
    
    print("\n🎯 修复完成")
    print("=" * 80)
    print("💡 如果问题仍然存在:")
    print("1. 重启应用程序")
    print("2. 检查GPU/CPU资源")
    print("3. 减少模型复杂度")
    print("4. 检查MT5连接状态")

if __name__ == '__main__':
    main()
