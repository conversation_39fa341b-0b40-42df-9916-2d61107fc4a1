#!/usr/bin/env python3
"""
测试AI推理交易配置界面功能
"""

import requests
import json
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.options import Options

def test_inference_config_ui():
    """测试推理配置界面"""
    
    print("🔧 测试AI推理交易配置界面")
    print("=" * 60)
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = None
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://127.0.0.1:5000/login")
        
        print("✅ 浏览器启动成功")
        
        # 登录
        username_input = driver.find_element(By.NAME, "username")
        password_input = driver.find_element(By.NAME, "password")
        login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
        
        username_input.send_keys("admin")
        password_input.send_keys("admin123")
        login_button.click()
        
        # 等待登录完成
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "sidebar"))
        )
        
        print("✅ 登录成功")
        
        # 导航到模型推理页面
        driver.get("http://127.0.0.1:5000/model-inference")
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "toggleInferenceBtn"))
        )
        
        print("✅ 推理页面加载成功")
        
        # 检查推理配置按钮是否存在
        config_button = driver.find_element(By.ID, "toggleInferenceBtn")
        print(f"✅ 找到推理配置按钮: {config_button.text}")
        
        # 点击推理配置按钮
        config_button.click()
        time.sleep(1)
        
        # 检查配置面板是否显示
        config_panel = driver.find_element(By.ID, "inferenceConfig")
        is_visible = config_panel.is_displayed()
        
        print(f"✅ 配置面板显示状态: {'显示' if is_visible else '隐藏'}")
        
        if is_visible:
            # 检查各个配置项
            config_items = {
                'inferenceTradeSize': '交易手数',
                'inferenceMinConfidence': '最低置信度',
                'inferenceStopLoss': '止损(pips)',
                'inferenceTakeProfit': '止盈(pips)',
                'inferenceTradeMode': '交易模式',
                'inferenceDynamicSL': '动态止盈止损',
                'inferenceTrailingStop': '移动止损',
                'inferencePreset': '配置预设'
            }
            
            print(f"\n📊 配置项检查:")
            all_found = True
            
            for element_id, name in config_items.items():
                try:
                    element = driver.find_element(By.ID, element_id)
                    value = element.get_attribute('value') if element.tag_name in ['input', 'select'] else element.is_selected()
                    print(f"   ✅ {name}: 找到 (当前值: {value})")
                except:
                    print(f"   ❌ {name}: 未找到")
                    all_found = False
            
            # 测试配置预设功能
            if all_found:
                print(f"\n🔧 测试配置预设功能:")
                
                preset_select = Select(driver.find_element(By.ID, "inferencePreset"))
                
                # 测试保守型预设
                preset_select.select_by_value("conservative")
                time.sleep(0.5)
                
                confidence = driver.find_element(By.ID, "inferenceMinConfidence").get_attribute('value')
                stop_loss = driver.find_element(By.ID, "inferenceStopLoss").get_attribute('value')
                take_profit = driver.find_element(By.ID, "inferenceTakeProfit").get_attribute('value')
                trade_mode = driver.find_element(By.ID, "inferenceTradeMode").get_attribute('value')
                
                print(f"   保守型预设:")
                print(f"     置信度: {confidence} (期望: 0.2)")
                print(f"     止损: {stop_loss} (期望: 30)")
                print(f"     止盈: {take_profit} (期望: 60)")
                print(f"     模式: {trade_mode} (期望: signal_only)")
                
                conservative_ok = (
                    float(confidence) == 0.2 and
                    int(stop_loss) == 30 and
                    int(take_profit) == 60 and
                    trade_mode == 'signal_only'
                )
                
                # 测试平衡型预设
                preset_select.select_by_value("balanced")
                time.sleep(0.5)
                
                confidence = driver.find_element(By.ID, "inferenceMinConfidence").get_attribute('value')
                stop_loss = driver.find_element(By.ID, "inferenceStopLoss").get_attribute('value')
                take_profit = driver.find_element(By.ID, "inferenceTakeProfit").get_attribute('value')
                trade_mode = driver.find_element(By.ID, "inferenceTradeMode").get_attribute('value')
                
                print(f"   平衡型预设:")
                print(f"     置信度: {confidence} (期望: 0.1)")
                print(f"     止损: {stop_loss} (期望: 50)")
                print(f"     止盈: {take_profit} (期望: 100)")
                print(f"     模式: {trade_mode} (期望: semi_auto)")
                
                balanced_ok = (
                    float(confidence) == 0.1 and
                    int(stop_loss) == 50 and
                    int(take_profit) == 100 and
                    trade_mode == 'semi_auto'
                )
                
                # 测试激进型预设
                preset_select.select_by_value("aggressive")
                time.sleep(0.5)
                
                confidence = driver.find_element(By.ID, "inferenceMinConfidence").get_attribute('value')
                stop_loss = driver.find_element(By.ID, "inferenceStopLoss").get_attribute('value')
                take_profit = driver.find_element(By.ID, "inferenceTakeProfit").get_attribute('value')
                trade_mode = driver.find_element(By.ID, "inferenceTradeMode").get_attribute('value')
                
                print(f"   激进型预设:")
                print(f"     置信度: {confidence} (期望: 0.05)")
                print(f"     止损: {stop_loss} (期望: 80)")
                print(f"     止盈: {take_profit} (期望: 150)")
                print(f"     模式: {trade_mode} (期望: auto_trade)")
                
                aggressive_ok = (
                    float(confidence) == 0.05 and
                    int(stop_loss) == 80 and
                    int(take_profit) == 150 and
                    trade_mode == 'auto_trade'
                )
                
                presets_ok = conservative_ok and balanced_ok and aggressive_ok
                
                print(f"\n📈 预设功能测试结果:")
                print(f"   保守型: {'✅通过' if conservative_ok else '❌失败'}")
                print(f"   平衡型: {'✅通过' if balanced_ok else '❌失败'}")
                print(f"   激进型: {'✅通过' if aggressive_ok else '❌失败'}")
                
                return all_found and presets_ok
            else:
                return False
        else:
            print("❌ 配置面板未显示")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    finally:
        if driver:
            driver.quit()

def show_config_summary():
    """显示配置功能总结"""
    
    print(f"\n📋 AI推理交易配置功能总结")
    print("=" * 80)
    
    print("🎛️ 配置界面位置:")
    print("• 页面: 深度学习模型推理")
    print("• 按钮: '推理配置' (蓝色按钮)")
    print("• 状态: 默认隐藏，点击按钮显示")
    
    print(f"\n🛡️ 风险管理配置:")
    print("1. ✅ 动态止盈止损")
    print("   • 复选框控制")
    print("   • 基于市场波动性和置信度自动调整")
    
    print("\n2. ✅ 移动止损")
    print("   • 复选框控制")
    print("   • 跟随价格移动保护利润")
    
    print(f"\n⚙️ 配置预设:")
    print("1. ✅ 保守型 (置信度20%)")
    print("   • 止损30pips, 止盈60pips")
    print("   • 仅信号提示模式")
    
    print("\n2. ✅ 平衡型 (置信度10%)")
    print("   • 止损50pips, 止盈100pips")
    print("   • 半自动交易模式")
    
    print("\n3. ✅ 激进型 (置信度5%)")
    print("   • 止损80pips, 止盈150pips")
    print("   • 自动交易模式")
    
    print(f"\n📊 完整配置项:")
    print("• 交易手数: 0.01-10手")
    print("• 最低置信度: 5%-99%")
    print("• 止损: 10-500pips")
    print("• 止盈: 10-1000pips")
    print("• 交易模式: 信号提示/半自动/自动")
    print("• 动态止盈止损: 开启/关闭")
    print("• 移动止损: 开启/关闭")
    print("• 配置预设: 保守/平衡/激进/自定义")

def main():
    """主函数"""
    
    print("🔧 AI推理交易配置界面测试")
    print("=" * 80)
    
    # 显示配置总结
    show_config_summary()
    
    print(f"\n🔍 开始界面功能测试...")
    
    try:
        # 测试界面功能
        success = test_inference_config_ui()
        
        print(f"\n📊 最终测试结果")
        print("=" * 80)
        
        if success:
            print("🎉 AI推理交易配置界面测试成功!")
            print("✅ 所有配置项都存在且可访问")
            print("✅ 配置预设功能正常工作")
            print("✅ 风险管理选项完整")
            
            print(f"\n💡 使用说明:")
            print("1. 进入'深度学习模型推理'页面")
            print("2. 点击'推理配置'按钮显示配置面板")
            print("3. 选择配置预设或自定义参数")
            print("4. 启用所需的风险管理功能")
            print("5. 开始推理时配置会自动应用")
            
        else:
            print("❌ 配置界面测试失败")
            print("⚠️ 可能需要检查浏览器驱动或页面加载")
            
    except ImportError:
        print("⚠️ 需要安装selenium: pip install selenium")
        print("⚠️ 需要下载ChromeDriver")
        
        print(f"\n📋 手动验证步骤:")
        print("1. 打开浏览器访问: http://127.0.0.1:5000/model-inference")
        print("2. 点击'推理配置'按钮")
        print("3. 检查是否显示以下配置项:")
        print("   • 交易手数、最低置信度")
        print("   • 止损、止盈")
        print("   • 交易模式")
        print("   • 动态止盈止损、移动止损")
        print("   • 配置预设")
        print("4. 测试配置预设是否能正确设置参数")

if __name__ == '__main__':
    main()
