#!/usr/bin/env python3
"""
测试推理和MT5自动连接修复
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_mt5_auto_connect():
    """测试MT5自动连接功能"""
    
    print("🔌 测试MT5自动连接功能")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 1. 检查当前连接状态
        print("📊 检查当前MT5连接状态...")
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        
        if response.status_code == 200:
            result = response.json()
            print(f"   当前状态: {'已连接' if result.get('connected') else '未连接'}")
        else:
            print(f"   状态检查失败: {response.status_code}")
        
        # 2. 测试自动连接API
        print("🔄 测试自动连接API...")
        response = session.post('http://127.0.0.1:5000/api/mt5/auto-connect')
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {result}")
            
            if result.get('success'):
                print(f"   ✅ 自动连接成功")
                if 'account_info' in result:
                    account = result['account_info']
                    print(f"   账户信息: {account.get('login')} - {account.get('name')}")
                return True
            else:
                print(f"   ❌ 自动连接失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试自动连接失败: {e}")
        return False

def test_model_inference():
    """测试模型推理功能"""
    
    print(f"\n🧠 测试模型推理功能")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 1. 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print(f"❌ 获取模型失败: {response.status_code}")
            return False
        
        models_result = response.json()
        if not models_result.get('success'):
            print(f"❌ 获取模型失败: {models_result.get('error')}")
            return False
        
        models = models_result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print(f"❌ 没有找到训练完成的模型")
            return False
        
        # 使用第一个完成的模型
        test_model = completed_models[0]
        print(f"   使用模型: {test_model['name']} ({test_model['symbol']}, {test_model['timeframe']})")
        
        # 2. 执行推理
        print("🔮 执行模型推理...")
        
        inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 50,
            'use_gpu': True,
            'show_confidence': True
        }
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/inference',
            json=inference_data
        )
        
        if response.status_code != 200:
            print(f"❌ 推理请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
        
        result = response.json()
        print(f"   推理响应: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}...")
        
        if result.get('success'):
            results = result.get('results', [])
            if results:
                print(f"   ✅ 推理成功: 获得 {len(results)} 个预测结果")
                
                # 显示前几个结果
                for i, res in enumerate(results[:3]):
                    print(f"   结果 {i+1}: {res['prediction']} @ {res['current_price']} (置信度: {res.get('confidence', 'N/A')})")
                
                return True
            else:
                print(f"   ❌ 推理成功但没有结果")
                return False
        else:
            print(f"   ❌ 推理失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理失败: {e}")
        return False

def test_inference_ui():
    """测试推理页面UI"""
    
    print(f"\n🖥️ 测试推理页面UI")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            # 检查关键UI元素
            ui_elements = [
                ('autoConnectMT5', '自动连接MT5按钮'),
                ('自动连接', '自动连接按钮文本'),
                ('mt5ConnectionStatus', 'MT5连接状态显示'),
                ('tradingModelSelect', '交易模型选择器'),
                ('inferenceInterval', '推理间隔选择器'),
                ('自动适配', '推理间隔自动适配选项')
            ]
            
            print(f"🔍 检查UI元素:")
            missing_elements = []
            
            for element_id, description in ui_elements:
                if element_id in html_content:
                    print(f"   ✅ {description}: 存在")
                else:
                    print(f"   ❌ {description}: 缺失")
                    missing_elements.append(description)
            
            return len(missing_elements) == 0
        else:
            print(f"❌ 推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试UI失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 推理和MT5自动连接修复测试")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("1. AI推理交易增加自动连接MT5功能")
    print("   • 新增自动连接按钮")
    print("   • 实现自动连接API调用")
    print("   • 改善连接状态显示")
    
    print("2. 修复深度学习模型推理无结果问题")
    print("   • 优先使用MT5真实数据")
    print("   • 改善模拟数据质量")
    print("   • 增强推理结果验证")
    print("   • 添加智能推理分析")
    
    # 测试MT5自动连接
    mt5_ok = test_mt5_auto_connect()
    
    # 测试模型推理
    inference_ok = test_model_inference()
    
    # 测试UI改进
    ui_ok = test_inference_ui()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if mt5_ok and inference_ok and ui_ok:
        print(f"🎉 所有修复都成功!")
        print(f"✅ MT5自动连接功能正常")
        print(f"✅ 模型推理功能正常")
        print(f"✅ UI界面改进正常")
        
        print(f"\n💡 使用说明:")
        print(f"🔌 MT5自动连接:")
        print(f"• 在AI推理交易区域点击'自动连接'按钮")
        print(f"• 系统会自动尝试连接MT5客户端")
        print(f"• 连接成功后状态会显示为'MT5已连接'")
        
        print(f"\n🧠 模型推理:")
        print(f"• 选择训练完成的模型")
        print(f"• 配置推理参数（实时/历史模式）")
        print(f"• 点击'开始推理'获得预测结果")
        print(f"• 推理结果包含预测方向、目标价格、置信度")
        
        print(f"\n🎯 改进亮点:")
        print(f"• 自动连接MT5，无需手动配置")
        print(f"• 优先使用真实MT5数据进行推理")
        print(f"• 智能分析价格趋势和技术指标")
        print(f"• 提供详细的推理结果和分析")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步检查")
        print(f"MT5自动连接: {'✅' if mt5_ok else '❌'}")
        print(f"模型推理: {'✅' if inference_ok else '❌'}")
        print(f"UI界面: {'✅' if ui_ok else '❌'}")
        
        print(f"\n🔧 故障排除:")
        if not mt5_ok:
            print(f"• 确保MT5客户端已安装并运行")
            print(f"• 检查MT5服务是否正常")
        if not inference_ok:
            print(f"• 确保有训练完成的模型")
            print(f"• 检查推理API的实现")
        if not ui_ok:
            print(f"• 检查前端JavaScript代码")
            print(f"• 确认HTML模板更新正确")
    
    print(f"\n🎯 下一步建议")
    print("=" * 80)
    
    print(f"📊 功能验证:")
    print(f"• 访问推理页面: http://127.0.0.1:5000/deep-learning/inference")
    print(f"• 测试自动连接MT5功能")
    print(f"• 执行模型推理并查看结果")
    print(f"• 验证交易模型选择功能")
    
    print(f"\n🔍 监控建议:")
    print(f"• 观察浏览器控制台日志")
    print(f"• 检查服务器端日志输出")
    print(f"• 监控MT5连接状态变化")
    print(f"• 验证推理结果的合理性")

if __name__ == '__main__':
    main()
