#!/usr/bin/env python3
"""
诊断深度学习训练进度问题
"""

import sqlite3
import requests
import json
import time
from datetime import datetime

def check_database_structure():
    """检查数据库结构"""
    
    print("🔍 检查训练任务数据库结构")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查training_tasks表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='training_tasks'
        """)
        
        if cursor.fetchone():
            print("✅ training_tasks表存在")
            
            # 查看表结构
            cursor.execute("PRAGMA table_info(training_tasks)")
            columns = cursor.fetchall()
            
            print("\n📋 表结构:")
            for col in columns:
                print(f"  - {col[1]}: {col[2]} {'(主键)' if col[5] else ''}")
            
            # 查看最近的训练任务
            cursor.execute("""
                SELECT id, model_id, status, progress, current_epoch, total_epochs,
                       train_loss, val_loss, created_at, updated_at
                FROM training_tasks 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            
            tasks = cursor.fetchall()
            
            print(f"\n📊 最近的训练任务 ({len(tasks)} 个):")
            for task in tasks:
                print(f"  ID: {task[0]}")
                print(f"  模型: {task[1]}")
                print(f"  状态: {task[2]}")
                print(f"  进度: {task[3]}%")
                print(f"  轮次: {task[4]}/{task[5]}")
                print(f"  损失: 训练={task[6]}, 验证={task[7]}")
                print(f"  创建: {task[8]}")
                print(f"  更新: {task[9]}")
                print("  " + "-" * 40)
                
        else:
            print("❌ training_tasks表不存在")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        return None

def test_training_progress_api():
    """测试训练进度API"""
    
    print("\n🧪 测试训练进度API")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 登录失败")
        return
    
    try:
        # 获取训练任务列表
        response = session.get('http://127.0.0.1:5000/api/deep-learning/training-tasks')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') and result.get('tasks'):
                tasks = result['tasks']
                print(f"✅ 获取到 {len(tasks)} 个训练任务")
                
                # 测试最新任务的进度API
                latest_task = tasks[0]
                task_id = latest_task['id']
                
                print(f"\n🔍 测试任务 {task_id} 的进度API:")
                print(f"   任务状态: {latest_task['status']}")
                print(f"   当前进度: {latest_task['progress']}%")
                
                # 调用进度API
                progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                
                if progress_response.status_code == 200:
                    progress_result = progress_response.json()
                    
                    if progress_result.get('success'):
                        progress = progress_result['progress']
                        print(f"✅ 进度API响应正常:")
                        print(f"   状态: {progress.get('status')}")
                        print(f"   进度: {progress.get('progress')}%")
                        print(f"   轮次: {progress.get('epoch')}/{progress.get('total_epochs')}")
                        print(f"   训练损失: {progress.get('train_loss')}")
                        print(f"   验证损失: {progress.get('val_loss')}")
                        
                        return task_id
                    else:
                        print(f"❌ 进度API返回错误: {progress_result.get('error')}")
                else:
                    print(f"❌ 进度API请求失败: {progress_response.status_code}")
                    print(f"响应内容: {progress_response.text[:200]}...")
                    
            else:
                print("❌ 没有找到训练任务")
                
        else:
            print(f"❌ 获取训练任务失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        
    return None

def simulate_training_progress_update():
    """模拟训练进度更新"""
    
    print("\n🔄 模拟训练进度更新")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找正在运行的训练任务
        cursor.execute("""
            SELECT id, model_id, current_epoch, total_epochs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        task = cursor.fetchone()
        
        if task:
            task_id, model_id, current_epoch, total_epochs = task
            print(f"✅ 找到运行中的任务: {task_id}")
            
            # 模拟进度更新
            for i in range(3):
                new_epoch = current_epoch + i + 1
                new_progress = min(95, 30 + (new_epoch / total_epochs) * 70)
                train_loss = 0.5 - (i * 0.1)
                val_loss = 0.6 - (i * 0.08)
                
                print(f"\n🔄 更新进度 {i+1}/3:")
                print(f"   轮次: {new_epoch}/{total_epochs}")
                print(f"   进度: {new_progress:.1f}%")
                print(f"   训练损失: {train_loss:.4f}")
                print(f"   验证损失: {val_loss:.4f}")
                
                # 更新数据库
                cursor.execute("""
                    UPDATE training_tasks
                    SET progress = ?, current_epoch = ?, train_loss = ?, val_loss = ?,
                        updated_at = ?
                    WHERE id = ?
                """, (new_progress, new_epoch, train_loss, val_loss, 
                      datetime.now().isoformat(), task_id))
                
                conn.commit()
                
                # 验证更新
                cursor.execute("""
                    SELECT progress, current_epoch, train_loss, val_loss, updated_at
                    FROM training_tasks WHERE id = ?
                """, (task_id,))
                
                updated_task = cursor.fetchone()
                if updated_task:
                    print(f"   ✅ 数据库已更新: 进度={updated_task[0]}%, 轮次={updated_task[1]}")
                else:
                    print(f"   ❌ 数据库更新失败")
                
                time.sleep(1)
                
        else:
            print("❌ 没有找到运行中的训练任务")
            
            # 创建一个测试任务
            print("\n🔧 创建测试训练任务...")
            
            test_task_id = f"test_task_{int(time.time())}"
            
            cursor.execute("""
                INSERT INTO training_tasks 
                (id, user_id, model_id, status, progress, current_epoch, total_epochs,
                 train_loss, val_loss, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (test_task_id, 1, 'test_model', 'running', 30.0, 5, 20,
                  0.5, 0.6, datetime.now().isoformat(), datetime.now().isoformat()))
            
            conn.commit()
            print(f"✅ 创建测试任务: {test_task_id}")
            
            return test_task_id
        
        conn.close()
        return task_id if task else None
        
    except Exception as e:
        print(f"❌ 模拟进度更新失败: {e}")
        return None

def test_frontend_progress_polling():
    """测试前端进度轮询"""
    
    print("\n🌐 测试前端进度轮询")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 登录失败")
        return
    
    # 获取一个任务ID进行测试
    task_id = test_training_progress_api()
    
    if not task_id:
        print("❌ 没有可测试的任务ID")
        return
    
    print(f"\n🔄 模拟前端轮询 (任务: {task_id})")
    
    for i in range(5):
        try:
            print(f"\n第 {i+1} 次轮询:")
            
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress = result['progress']
                    print(f"  ✅ 进度: {progress.get('progress', 0)}%")
                    print(f"  📊 轮次: {progress.get('epoch', 0)}/{progress.get('total_epochs', 0)}")
                    print(f"  📈 损失: 训练={progress.get('train_loss', 'N/A')}, 验证={progress.get('val_loss', 'N/A')}")
                    print(f"  🔄 状态: {progress.get('status', 'unknown')}")
                else:
                    print(f"  ❌ API错误: {result.get('error')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 轮询异常: {e}")
        
        time.sleep(2)  # 模拟前端2秒轮询间隔

def main():
    """主函数"""
    
    print("🔧 深度学习训练进度诊断")
    print("=" * 60)
    
    # 检查数据库结构
    db_ok = check_database_structure()
    
    if not db_ok:
        print("\n❌ 数据库结构有问题，无法继续诊断")
        return
    
    # 测试API
    test_training_progress_api()
    
    # 模拟进度更新
    test_task_id = simulate_training_progress_update()
    
    # 测试前端轮询
    test_frontend_progress_polling()
    
    print(f"\n📋 诊断总结")
    print("=" * 60)
    
    print(f"🔧 可能的问题原因:")
    print(f"1. 训练进程没有正确更新数据库")
    print(f"2. 前端轮询间隔过长或停止")
    print(f"3. API响应有问题")
    print(f"4. 数据库连接问题")
    print(f"5. 训练任务状态异常")
    
    print(f"\n💡 建议的解决方案:")
    print(f"• 检查训练进程是否正常运行")
    print(f"• 确认数据库更新逻辑正确")
    print(f"• 验证前端轮询代码")
    print(f"• 添加更多调试日志")
    print(f"• 检查训练回调函数")

if __name__ == '__main__':
    main()
