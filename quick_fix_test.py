#!/usr/bin/env python3
"""
快速修复测试
"""

import sqlite3
import requests

def create_table():
    """创建缺失的表"""
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auto_trading_sessions (
                id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                user_id INTEGER NOT NULL,
                model_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                timeframe TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP,
                total_trades INTEGER DEFAULT 0,
                total_profit REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ 数据库表创建成功")
        return True
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        return False

def test_status_api():
    """测试状态API"""
    try:
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 自动交易状态API正常")
                return True
        
        print("❌ 自动交易状态API异常")
        return False
    except Exception as e:
        print(f"❌ 测试状态API失败: {e}")
        return False

def test_backtest():
    """测试回测"""
    try:
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 获取模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用模型")
            return False
        
        test_model = completed_models[0]
        
        # 执行回测
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'start_date': '2024-07-01',
            'end_date': '2024-07-29',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.5
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', json=backtest_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                trades = result.get('trades', [])
                stats = result.get('statistics', {})
                print(f"✅ 回测成功: {len(trades)} 笔交易, 收益: {stats.get('total_return', 0):.2f}%")
                return True
            else:
                print(f"❌ 回测失败: {result.get('error')}")
                return False
        
        print("❌ 回测API请求失败")
        return False
    except Exception as e:
        print(f"❌ 测试回测失败: {e}")
        return False

def main():
    print("🔧 快速修复测试")
    print("=" * 40)
    
    # 1. 创建数据库表
    db_ok = create_table()
    
    # 2. 测试状态API
    status_ok = test_status_api()
    
    # 3. 测试回测
    backtest_ok = test_backtest()
    
    print(f"\n📋 结果:")
    print(f"数据库: {'✅' if db_ok else '❌'}")
    print(f"状态API: {'✅' if status_ok else '❌'}")
    print(f"回测: {'✅' if backtest_ok else '❌'}")
    
    if db_ok and status_ok and backtest_ok:
        print("\n🎉 所有问题都已修复!")
    else:
        print("\n⚠️ 部分问题仍需解决")

if __name__ == '__main__':
    main()
