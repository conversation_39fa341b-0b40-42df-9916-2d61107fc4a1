#!/usr/bin/env python3
"""
AI训练优化功能测试脚本
验证数据缓存、检查点机制、监控系统等功能
"""

import os
import sys
import json
import time
import tempfile
import shutil
from datetime import datetime
from typing import Dict, Any

# 添加服务路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from services.deep_learning_service import DeepLearningService
    import numpy as np
    import torch
except ImportError as e:
    print(f"❌ 导入依赖失败: {e}")
    sys.exit(1)

class TrainingOptimizationTester:
    """训练优化功能测试器"""
    
    def __init__(self):
        self.dl_service = DeepLearningService()
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    def test_data_caching(self):
        """测试数据缓存功能"""
        print("\n🔍 测试数据缓存功能")
        print("-" * 40)
        
        try:
            # 创建测试配置
            test_config = {
                'symbol': 'XAUUSD',
                'timeframe': '5m',
                'sequence_length': 30,
                'data_config': {
                    'start_date': '2024-01-01',
                    'end_date': '2024-01-31'
                }
            }
            
            # 测试缓存键生成
            cache_key = self.dl_service._generate_cache_key(test_config)
            self.log_test_result(
                "缓存键生成", 
                len(cache_key) == 32,  # MD5哈希长度
                f"生成的缓存键: {cache_key}"
            )
            
            # 测试缓存保存和加载
            test_data = {
                'X_train': np.random.rand(100, 30, 5),
                'X_val': np.random.rand(20, 30, 5),
                'y_train': np.random.randint(0, 2, 100),
                'y_val': np.random.randint(0, 2, 20),
                'feature_names': ['open', 'high', 'low', 'close', 'volume'],
                'data_info': {'test': True}
            }
            
            # 保存到缓存
            self.dl_service._save_data_cache(cache_key, test_data)
            
            # 从缓存加载
            loaded_data = self.dl_service._load_data_cache(cache_key)
            
            cache_success = loaded_data is not None
            self.log_test_result(
                "数据缓存保存/加载",
                cache_success,
                "缓存数据保存和加载成功" if cache_success else "缓存操作失败"
            )
            
            # 测试数据完整性验证
            if loaded_data:
                integrity_ok = self.dl_service._validate_data_integrity(loaded_data)
                self.log_test_result(
                    "数据完整性验证",
                    integrity_ok,
                    "数据完整性验证通过" if integrity_ok else "数据完整性验证失败"
                )
            
            # 清理测试缓存
            cache_file = os.path.join(self.dl_service.cache_path, f"{cache_key}.pkl")
            if os.path.exists(cache_file):
                os.remove(cache_file)
                
        except Exception as e:
            self.log_test_result("数据缓存功能", False, f"测试异常: {str(e)}")
    
    def test_checkpoint_mechanism(self):
        """测试检查点机制"""
        print("\n🔍 测试检查点机制")
        print("-" * 40)
        
        try:
            # 创建临时检查点目录
            temp_dir = tempfile.mkdtemp()
            checkpoint_path = os.path.join(temp_dir, 'test_checkpoint.pt')
            
            # 创建测试模型状态
            test_checkpoint = {
                'epoch': 10,
                'model_state_dict': {'test_param': torch.randn(10, 10)},
                'optimizer_state_dict': {'test_opt': 'test_value'},
                'best_val_loss': 0.5,
                'history': {'train_loss': [0.8, 0.7, 0.6]},
                'patience_counter': 2,
                'config': {'test': True}
            }
            
            # 保存检查点
            torch.save(test_checkpoint, checkpoint_path)
            
            # 加载检查点
            loaded_checkpoint = torch.load(checkpoint_path, map_location='cpu')
            
            checkpoint_success = (
                loaded_checkpoint['epoch'] == 10 and
                loaded_checkpoint['best_val_loss'] == 0.5 and
                len(loaded_checkpoint['history']['train_loss']) == 3
            )
            
            self.log_test_result(
                "检查点保存/加载",
                checkpoint_success,
                "检查点机制正常工作" if checkpoint_success else "检查点机制异常"
            )
            
            # 清理临时文件
            shutil.rmtree(temp_dir)
            
        except Exception as e:
            self.log_test_result("检查点机制", False, f"测试异常: {str(e)}")
    
    def test_system_monitoring(self):
        """测试系统监控功能"""
        print("\n🔍 测试系统监控功能")
        print("-" * 40)
        
        try:
            # 等待监控数据更新
            time.sleep(2)
            
            # 检查系统监控数据
            monitor_data = self.dl_service.system_monitor
            
            has_cpu_data = 'cpu_percent' in monitor_data
            has_memory_data = 'memory_percent' in monitor_data
            has_timestamp = 'timestamp' in monitor_data
            
            self.log_test_result(
                "系统资源监控",
                has_cpu_data and has_memory_data and has_timestamp,
                f"CPU: {monitor_data.get('cpu_percent', 'N/A')}%, 内存: {monitor_data.get('memory_percent', 'N/A')}%"
            )
            
            # 测试系统状态获取
            system_status = self.dl_service.get_system_status()
            
            status_success = (
                'timestamp' in system_status and
                'system_monitor' in system_status and
                'device' in system_status
            )
            
            self.log_test_result(
                "系统状态获取",
                status_success,
                f"设备: {system_status.get('device', 'N/A')}"
            )
            
        except Exception as e:
            self.log_test_result("系统监控功能", False, f"测试异常: {str(e)}")
    
    def test_training_metrics(self):
        """测试训练指标记录"""
        print("\n🔍 测试训练指标记录")
        print("-" * 40)
        
        try:
            test_task_id = "test_task_12345"
            
            # 记录测试指标
            test_metrics = {
                'progress': 50.0,
                'epoch': 25,
                'train_loss': 0.3,
                'val_loss': 0.35,
                'timestamp': datetime.now().isoformat()
            }
            
            self.dl_service._record_training_metrics(test_task_id, test_metrics)
            
            # 读取指标
            recorded_metrics = self.dl_service.get_training_metrics(test_task_id)
            
            metrics_success = (
                len(recorded_metrics) > 0 and
                recorded_metrics[-1]['progress'] == 50.0 and
                recorded_metrics[-1]['epoch'] == 25
            )
            
            self.log_test_result(
                "训练指标记录",
                metrics_success,
                f"成功记录和读取 {len(recorded_metrics)} 条指标"
            )
            
            # 清理测试文件
            metrics_file = os.path.join(
                self.dl_service.models_path, 
                'metrics', 
                f"{test_task_id}_metrics.jsonl"
            )
            if os.path.exists(metrics_file):
                os.remove(metrics_file)
                
        except Exception as e:
            self.log_test_result("训练指标记录", False, f"测试异常: {str(e)}")
    
    def test_alert_system(self):
        """测试告警系统"""
        print("\n🔍 测试告警系统")
        print("-" * 40)
        
        try:
            test_task_id = "test_alert_task"
            
            # 测试告警发送
            self.dl_service._send_training_alert(
                test_task_id, 
                "test_alert", 
                {"test": "alert_data"}
            )
            
            self.log_test_result(
                "告警系统",
                True,  # 如果没有异常就认为成功
                "告警系统正常工作"
            )
            
        except Exception as e:
            self.log_test_result("告警系统", False, f"测试异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始AI训练优化功能测试")
        print("=" * 60)
        
        # 运行各项测试
        self.test_data_caching()
        self.test_checkpoint_mechanism()
        self.test_system_monitoring()
        self.test_training_metrics()
        self.test_alert_system()
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 测试结果统计")
        print("=" * 40)
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 显示失败的测试
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test_name']}: {result['message']}")
        
        # 保存测试报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': passed_tests/total_tests*100
                },
                'results': self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细测试报告已保存到: {report_file}")
        
        return failed_tests == 0

def main():
    """主函数"""
    print("🚀 AI训练优化功能测试工具")
    print("测试数据缓存、检查点机制、系统监控等功能")
    print()
    
    tester = TrainingOptimizationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！AI训练优化功能正常工作。")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit(main())
