<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实AI策略模型集成验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .feature-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 15px 0;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
        }
        .before-after {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .before, .after {
            flex: 1;
            padding: 10px;
            border-radius: 4px;
        }
        .before {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .after {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .api-flow {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #17a2b8;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        .strategy-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            background: #f8f9fa;
        }
        .strategy-card.user {
            border-color: #28a745;
            background-color: #f0fff4;
        }
        .strategy-card.shared {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
    </style>
</head>
<body>
    <h1>真实AI策略模型集成验证</h1>
    
    <div class="test-container">
        <h3>✅ 真实AI策略模型集成完成</h3>
        <div class="alert alert-success">
            <h6>更新内容：</h6>
            <ul class="mb-0">
                <li>✅ <strong>数据源切换</strong>：从模拟数据切换到真实的Strategy表数据</li>
                <li>✅ <strong>策略模型获取</strong>：获取用户训练完成的AI策略和管理员分享的策略</li>
                <li>✅ <strong>智能预测</strong>：基于策略历史性能和特征生成预测</li>
                <li>✅ <strong>权限控制</strong>：只显示用户有权限访问的策略模型</li>
                <li>✅ <strong>性能指标</strong>：显示真实的胜率、盈利因子等性能数据</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h3>数据源对比</h3>
        
        <div class="feature-item">
            <h6><i class="fas fa-database text-primary"></i> 数据源变更</h6>
            <div class="before-after">
                <div class="before">
                    <strong>修改前：</strong><br>
                    • 使用硬编码的模拟数据<br>
                    • 固定的3个示例模型<br>
                    • 虚假的性能指标<br>
                    • 无法反映真实策略
                </div>
                <div class="after">
                    <strong>修改后：</strong><br>
                    • 从Strategy表获取真实数据<br>
                    • 用户实际训练的策略模型<br>
                    • 真实的历史性能指标<br>
                    • 管理员分享的策略模型
                </div>
            </div>
        </div>
        
        <div class="feature-item">
            <h6><i class="fas fa-users text-success"></i> 策略来源</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="strategy-card user">
                        <h6><i class="fas fa-user text-success"></i> 用户策略</h6>
                        <ul class="mb-0">
                            <li>用户自己训练的AI策略</li>
                            <li>状态为"completed"的策略</li>
                            <li>按创建时间倒序排列</li>
                            <li>显示"我的策略"标识</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="strategy-card shared">
                        <h6><i class="fas fa-share text-primary"></i> 分享策略</h6>
                        <ul class="mb-0">
                            <li>管理员分享的AI策略</li>
                            <li>is_shared=True的策略</li>
                            <li>按分享时间倒序排列</li>
                            <li>显示"管理员分享"标识</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>API实现详解</h3>
        
        <div class="feature-item">
            <h6><i class="fas fa-code text-info"></i> 后端API更新</h6>
            <div class="api-flow">
                <h6>📡 /api/ai-models/user-models</h6>
                <div class="code-block">
# 查询用户策略
user_strategies = Strategy.query.filter_by(
    user_id=user_id,
    strategy_type='ai',
    status='completed'
).order_by(Strategy.created_at.desc()).all()

# 查询分享策略
shared_strategies = Strategy.query.filter_by(
    strategy_type='ai',
    status='completed',
    is_shared=True
).order_by(Strategy.shared_at.desc()).all()
                </div>
            </div>
            
            <div class="api-flow">
                <h6>🔮 /api/ai-models/predict</h6>
                <div class="code-block">
# 基于策略特征生成预测
performance_metrics = strategy.get_performance_metrics()
win_rate = performance_metrics.get('win_rate', 0.5)
profit_factor = performance_metrics.get('profit_factor', 1.0)

# 计算策略质量分数
quality_score = (win_rate * 0.4 + 
               min(profit_factor / 2.0, 1.0) * 0.3 + 
               min(sharpe_ratio / 2.0, 1.0) * 0.3)
                </div>
            </div>
        </div>
        
        <div class="feature-item">
            <h6><i class="fas fa-brain text-warning"></i> 智能预测逻辑</h6>
            <div class="alert alert-primary">
                <h6>🎯 预测算法特点：</h6>
                <ol class="mb-2">
                    <li><strong>策略质量评估</strong>：基于胜率、盈利因子、夏普比率计算质量分数</li>
                    <li><strong>置信度调整</strong>：高质量策略提供更高的预测置信度</li>
                    <li><strong>方向倾向</strong>：基于策略历史表现调整看涨/看跌倾向</li>
                    <li><strong>技术指标融合</strong>：结合RSI等技术指标优化预测</li>
                </ol>
                <h6>📊 性能指标应用：</h6>
                <ul class="mb-0">
                    <li><strong>胜率 > 60%</strong>：增加预测置信度和方向性</li>
                    <li><strong>盈利因子 > 1.2</strong>：倾向于看涨信号</li>
                    <li><strong>夏普比率</strong>：影响预测的稳定性评估</li>
                    <li><strong>最大回撤</strong>：调整风险评估参数</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>数据结构映射</h3>
        
        <div class="feature-item">
            <h6><i class="fas fa-table text-success"></i> Strategy表字段映射</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <h6>基础信息：</h6>
                        <ul class="mb-0">
                            <li><strong>id</strong> → strategy_{id}</li>
                            <li><strong>name</strong> → 模型名称</li>
                            <li><strong>description</strong> → 模型描述</li>
                            <li><strong>ai_model</strong> → AI模型类型</li>
                            <li><strong>timeframe</strong> → 数据时间框架</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <h6>性能指标：</h6>
                        <ul class="mb-0">
                            <li><strong>win_rate</strong> → 胜率/成功率</li>
                            <li><strong>profit_factor</strong> → 盈利因子</li>
                            <li><strong>sharpe_ratio</strong> → 夏普比率</li>
                            <li><strong>max_drawdown</strong> → 最大回撤</li>
                            <li><strong>total_trades</strong> → 总交易次数</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="feature-item">
            <h6><i class="fas fa-cogs text-warning"></i> 分析维度解析</h6>
            <div class="code-block">
# 从training_data中提取分析维度
training_data = json.loads(strategy.training_data)
dimensions_config = training_data['analysis_dimensions']

analysis_dimensions = []
if dimensions_config.get('technical_indicators'):
    analysis_dimensions.append('技术指标')
if dimensions_config.get('price_action'):
    analysis_dimensions.append('价格行为')
if dimensions_config.get('volume_analysis'):
    analysis_dimensions.append('成交量分析')
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>验证步骤</h3>
        <div class="alert alert-info">
            <h6>🔍 如何验证真实策略模型集成：</h6>
            <ol class="mb-2">
                <li><strong>检查数据来源</strong>：
                    <ul>
                        <li>确认不再显示模拟的示例模型</li>
                        <li>只显示用户实际训练的策略</li>
                        <li>包含管理员分享的策略（如果有）</li>
                    </ul>
                </li>
                <li><strong>验证策略信息</strong>：
                    <ul>
                        <li>策略名称与训练AI策略页面一致</li>
                        <li>性能指标显示真实的历史数据</li>
                        <li>分析维度反映训练时的配置</li>
                    </ul>
                </li>
                <li><strong>测试预测功能</strong>：
                    <ul>
                        <li>选择真实策略后启动监测</li>
                        <li>观察预测结果的推理说明</li>
                        <li>验证置信度与策略性能的关联</li>
                    </ul>
                </li>
                <li><strong>权限验证</strong>：
                    <ul>
                        <li>只能看到自己的策略和分享策略</li>
                        <li>无法访问其他用户的私有策略</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>
    
    <div class="test-container">
        <h3>预期效果</h3>
        <div class="alert alert-success">
            <h6>✅ 功能改进：</h6>
            <ul class="mb-2">
                <li><strong>真实性</strong>：使用用户实际训练的AI策略模型</li>
                <li><strong>个性化</strong>：反映用户的交易策略和偏好</li>
                <li><strong>准确性</strong>：基于真实历史性能生成预测</li>
                <li><strong>可信度</strong>：预测质量与策略表现相关</li>
            </ul>
            <h6>🎯 用户体验：</h6>
            <ul class="mb-0">
                <li>看到自己训练的策略在实际应用中发挥作用</li>
                <li>基于策略历史表现评估预测可信度</li>
                <li>享受个性化的AI预测服务</li>
                <li>获得更贴合个人交易风格的信号</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h3>注意事项</h3>
        <div class="alert alert-warning">
            <h6>⚠️ 使用说明：</h6>
            <ul class="mb-2">
                <li>如果没有训练完成的AI策略，将无法使用AI模型信号功能</li>
                <li>策略的预测质量与其历史表现直接相关</li>
                <li>建议优先选择胜率和盈利因子较高的策略</li>
                <li>管理员分享的策略可能更加稳定和可靠</li>
            </ul>
            <h6>🔧 故障排除：</h6>
            <ul class="mb-0">
                <li>如果看不到策略列表，请检查是否有已完成训练的策略</li>
                <li>如果预测失败，会自动回退到基于策略特征的预测</li>
                <li>策略权限问题会在API层面进行验证和拦截</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 真实AI策略模型集成验证页面已加载');
            
            // 显示集成完成的通知
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                notification.innerHTML = `
                    <strong>✅ 真实AI策略模型集成完成！</strong><br>
                    现在使用用户训练的真实AI策略进行预测。
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(notification);
                
                // 5秒后自动移除
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 5000);
            }, 1000);
        });
    </script>
</body>
</html>
