#!/usr/bin/env python3
"""
测试增强的AI推理回测分析功能
"""

import requests
import json
import time

def test_enhanced_backtest_analysis():
    """测试增强的回测分析功能"""
    
    print("🔧 测试增强的AI推理回测分析")
    print("=" * 70)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        print(f"✅ 使用模型: {test_model['name']}")
        
        # 测试不同配置的回测
        test_configs = [
            {
                'name': '激进配置 (低置信度)',
                'config': {
                    'initial_balance': 10000,
                    'lot_size': 0.01,
                    'stop_loss_pips': 50,
                    'take_profit_pips': 100,
                    'min_confidence': 0.05  # 低置信度，期望更多交易
                }
            },
            {
                'name': '平衡配置 (中等置信度)',
                'config': {
                    'initial_balance': 10000,
                    'lot_size': 0.01,
                    'stop_loss_pips': 40,
                    'take_profit_pips': 80,
                    'min_confidence': 0.1  # 中等置信度
                }
            }
        ]
        
        results = []
        
        for test_case in test_configs:
            print(f"\n🔍 测试: {test_case['name']}")
            config = test_case['config']
            
            print(f"   配置: 置信度{config['min_confidence']*100:.0f}%, 止损{config['stop_loss_pips']}, 止盈{config['take_profit_pips']}")
            
            backtest_data = {
                'model_id': test_model['id'],
                'symbol': test_model['symbol'],
                'timeframe': test_model['timeframe'],
                'start_date': '2025-07-25',
                'end_date': '2025-07-29',
                **config
            }
            
            start_time = time.time()
            response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                                   json=backtest_data)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    trades = result.get('trades', [])
                    stats = result.get('statistics', {})
                    
                    print(f"   ✅ 回测成功 (耗时: {end_time - start_time:.1f}秒)")
                    
                    # 显示增强的统计信息
                    print(f"   📊 详细统计分析:")
                    print(f"      总交易数: {stats.get('total_trades', 0)} 笔")
                    print(f"      盈利单数: {stats.get('winning_trades', 0)} 单")
                    print(f"      亏损单数: {stats.get('losing_trades', 0)} 单")
                    print(f"      平仓单数: {stats.get('break_even_trades', 0)} 单")
                    
                    print(f"   💰 盈亏分析:")
                    print(f"      总盈利: ${stats.get('gross_profit', 0):.2f}")
                    print(f"      总亏损: ${stats.get('gross_loss', 0):.2f}")
                    print(f"      净盈利: ${stats.get('net_profit', 0):.2f}")
                    print(f"      总收益率: {stats.get('total_return', 0):.2f}%")
                    
                    print(f"   📈 性能指标:")
                    print(f"      胜率: {stats.get('win_rate', 0):.1f}%")
                    print(f"      平均盈利: ${stats.get('average_win', 0):.2f}")
                    print(f"      平均亏损: ${stats.get('average_loss', 0):.2f}")
                    print(f"      盈亏比: {stats.get('reward_risk_ratio', 0):.2f}")
                    print(f"      盈利因子: {stats.get('profit_factor', 0):.2f}")
                    
                    print(f"   🎯 极值统计:")
                    print(f"      最大单笔盈利: ${stats.get('max_win', 0):.2f}")
                    print(f"      最大单笔亏损: ${abs(stats.get('max_loss', 0)):.2f}")
                    print(f"      最大连续盈利: {stats.get('max_consecutive_wins', 0)} 单")
                    print(f"      最大连续亏损: {stats.get('max_consecutive_losses', 0)} 单")
                    print(f"      最大回撤: {stats.get('max_drawdown', 0):.2f}%")
                    
                    # 验证关键统计字段
                    required_fields = [
                        'total_trades', 'winning_trades', 'losing_trades', 
                        'gross_profit', 'gross_loss', 'net_profit',
                        'win_rate', 'average_win', 'average_loss',
                        'profit_factor', 'max_win', 'max_loss'
                    ]
                    
                    missing_fields = [field for field in required_fields if field not in stats]
                    if missing_fields:
                        print(f"   ❌ 缺少统计字段: {missing_fields}")
                    else:
                        print(f"   ✅ 所有统计字段完整")
                    
                    # 验证数据一致性
                    calculated_net = stats.get('gross_profit', 0) - stats.get('gross_loss', 0)
                    actual_net = stats.get('net_profit', 0)
                    if abs(calculated_net - actual_net) < 0.01:
                        print(f"   ✅ 净盈利计算正确: ${actual_net:.2f}")
                    else:
                        print(f"   ❌ 净盈利计算错误: 期望${calculated_net:.2f}, 实际${actual_net:.2f}")
                    
                    results.append({
                        'name': test_case['name'],
                        'success': True,
                        'stats': stats,
                        'trades_count': len(trades),
                        'has_all_fields': len(missing_fields) == 0,
                        'net_profit_correct': abs(calculated_net - actual_net) < 0.01
                    })
                    
                else:
                    print(f"   ❌ 回测失败: {result.get('error')}")
                    results.append({
                        'name': test_case['name'],
                        'success': False,
                        'error': result.get('error')
                    })
            else:
                print(f"   ❌ 请求失败: HTTP {response.status_code}")
                results.append({
                    'name': test_case['name'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
        
        # 分析测试结果
        if len(results) > 0:
            print(f"\n📊 测试结果汇总:")
            print(f"{'配置':<25} {'状态':<8} {'交易数':<8} {'净盈利':<12} {'胜率':<8} {'字段完整':<10}")
            print("-" * 80)
            
            success_count = 0
            for r in results:
                if r['success']:
                    status = "✅成功"
                    trades = r.get('trades_count', 0)
                    net_profit = f"${r['stats'].get('net_profit', 0):.2f}"
                    win_rate = f"{r['stats'].get('win_rate', 0):.1f}%"
                    fields_ok = "✅是" if r.get('has_all_fields') else "❌否"
                    success_count += 1
                else:
                    status = "❌失败"
                    trades = 0
                    net_profit = "N/A"
                    win_rate = "N/A"
                    fields_ok = "N/A"
                
                print(f"{r['name']:<25} {status:<8} {trades:<8} {net_profit:<12} {win_rate:<8} {fields_ok:<10}")
            
            # 统计成功率
            success_rate = success_count / len(results) * 100
            
            print(f"\n📈 测试统计:")
            print(f"• 总测试数: {len(results)}")
            print(f"• 成功数: {success_count}")
            print(f"• 成功率: {success_rate:.1f}%")
            
            # 验证增强功能
            enhanced_features_ok = all([
                r.get('has_all_fields', False) and r.get('net_profit_correct', False) 
                for r in results if r['success']
            ])
            
            if enhanced_features_ok:
                print(f"✅ 增强统计功能全部正常")
            else:
                print(f"⚠️ 部分增强功能需要检查")
            
            return success_rate >= 50 and enhanced_features_ok
        else:
            print(f"\n⚠️ 没有测试结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def show_enhancement_summary():
    """显示增强功能总结"""
    
    print(f"\n📋 AI推理回测分析增强功能")
    print("=" * 60)
    
    print("🎨 新增统计指标:")
    print("1. ✅ 基础盈亏统计")
    print("   • 盈利单数 / 亏损单数 / 平仓单数")
    print("   • 总盈利 / 总亏损 / 净盈利")
    print("   • 胜率 / 总收益率")
    
    print("\n2. ✅ 平均值分析")
    print("   • 平均盈利 / 平均亏损")
    print("   • 盈亏比 (平均盈利/平均亏损)")
    print("   • 盈利因子 (总盈利/总亏损)")
    
    print("\n3. ✅ 极值统计")
    print("   • 最大单笔盈利 / 最大单笔亏损")
    print("   • 最大连续盈利单数 / 最大连续亏损单数")
    print("   • 最大回撤百分比")
    
    print("\n4. ✅ 可视化展示")
    print("   • 多层级统计卡片显示")
    print("   • 详细统计表格")
    print("   • 盈亏分布进度条图表")
    print("   • 颜色编码 (盈利绿色, 亏损红色)")
    
    print("\n🔧 技术改进:")
    print("• 后端: 增强统计计算函数")
    print("• 前端: 重新设计结果显示界面")
    print("• 数据: 添加数据一致性验证")
    print("• 体验: 提供更直观的分析视图")

def main():
    """主函数"""
    
    print("🔧 AI推理回测分析增强测试")
    print("=" * 80)
    
    # 显示增强功能总结
    show_enhancement_summary()
    
    # 测试增强功能
    success = test_enhanced_backtest_analysis()
    
    print(f"\n📊 最终测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 AI推理回测分析增强成功!")
        print("✅ 详细统计指标全部正常")
        print("✅ 盈利/亏损单数统计准确")
        print("✅ 净盈利计算正确")
        print("✅ 前端显示界面完善")
        
        print(f"\n💡 用户体验改善:")
        print("• 一目了然的盈亏分布")
        print("• 详细的统计分析表格")
        print("• 直观的可视化图表")
        print("• 专业的交易分析指标")
        
        print(f"\n🎯 分析价值:")
        print("• 盈利单数 vs 亏损单数: 了解交易成功率")
        print("• 总盈利 vs 总亏损: 评估策略盈利能力")
        print("• 净盈利 (盈利-亏损): 最终收益评估")
        print("• 平均盈利 vs 平均亏损: 单笔交易质量")
        print("• 连续盈亏统计: 策略稳定性分析")
        
    else:
        print("❌ 测试失败")
        print("⚠️ 增强功能可能需要进一步调试")
        
        print(f"\n🔧 故障排除:")
        print("• 检查后端统计计算函数")
        print("• 验证前端显示逻辑")
        print("• 确认数据字段完整性")
        print("• 测试不同配置组合")

if __name__ == '__main__':
    main()
