#!/usr/bin/env python3
"""
测试GPU监控页面修复
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_gpu_status_api():
    """测试GPU状态API"""
    
    print("🎮 测试GPU状态API")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        print(f"📊 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 API响应成功: {result.get('success')}")
            
            if result.get('success'):
                gpu_status = result.get('gpu_status', {})
                
                print(f"✅ GPU状态API修复成功!")
                print(f"📋 GPU状态详情:")
                print(f"   GPU可用: {gpu_status.get('gpu_available')}")
                print(f"   GPU名称: {gpu_status.get('gpu_name')}")
                print(f"   CUDA版本: {gpu_status.get('cuda_version')}")
                print(f"   PyTorch版本: {gpu_status.get('pytorch_version')}")
                print(f"   总内存: {gpu_status.get('memory_total', 0):.1f} GB")
                print(f"   已用内存: {gpu_status.get('memory_used', 0):.1f} GB")
                print(f"   可用内存: {gpu_status.get('memory_free', 0):.1f} GB")
                print(f"   内存使用率: {gpu_status.get('memory_usage_percent', 0):.1f}%")
                print(f"   GPU使用率: {gpu_status.get('gpu_utilization', 0):.1f}%")
                print(f"   温度: {gpu_status.get('temperature', 0):.1f}°C")
                print(f"   功耗: {gpu_status.get('power_usage', 0):.0f}W")
                
                # 验证必要字段
                required_fields = ['gpu_available', 'gpu_name', 'memory_total', 'memory_used', 
                                 'memory_free', 'memory_usage_percent', 'gpu_utilization', 
                                 'temperature', 'power_usage']
                
                missing_fields = []
                for field in required_fields:
                    if field not in gpu_status:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"⚠️ 缺少字段: {missing_fields}")
                    return False
                else:
                    print(f"✅ 所有必要字段都存在")
                    return True
                
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_requests():
    """测试多次请求的稳定性"""
    
    print(f"\n🔄 测试API稳定性 (多次请求)")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    success_count = 0
    total_requests = 5
    
    for i in range(total_requests):
        try:
            response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    success_count += 1
                    print(f"   请求 {i+1}: ✅ 成功")
                else:
                    print(f"   请求 {i+1}: ❌ API错误 - {result.get('error')}")
            else:
                print(f"   请求 {i+1}: ❌ HTTP错误 - {response.status_code}")
                
            time.sleep(0.5)  # 间隔0.5秒
            
        except Exception as e:
            print(f"   请求 {i+1}: ❌ 异常 - {e}")
    
    success_rate = (success_count / total_requests) * 100
    print(f"\n📊 稳定性测试结果:")
    print(f"   成功请求: {success_count}/{total_requests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    return success_rate >= 80

def simulate_frontend_usage():
    """模拟前端使用场景"""
    
    print(f"\n🖥️ 模拟前端使用场景")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 模拟前端定期获取GPU状态
        print("📊 模拟前端定期获取GPU状态...")
        
        for i in range(3):
            response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    gpu_status = result.get('gpu_status', {})
                    
                    # 模拟前端处理逻辑
                    if gpu_status.get('gpu_available'):
                        gpu_usage = gpu_status.get('gpu_utilization', 0)
                        memory_usage = gpu_status.get('memory_usage_percent', 0)
                        temperature = gpu_status.get('temperature', 0)
                        power_usage = gpu_status.get('power_usage', 0)
                        
                        print(f"   更新 {i+1}: GPU {gpu_usage:.1f}% | 内存 {memory_usage:.1f}% | 温度 {temperature:.1f}°C | 功耗 {power_usage:.0f}W")
                    else:
                        print(f"   更新 {i+1}: GPU不可用")
                else:
                    print(f"   更新 {i+1}: API错误 - {result.get('error')}")
                    return False
            else:
                print(f"   更新 {i+1}: HTTP错误 - {response.status_code}")
                return False
            
            time.sleep(1)  # 间隔1秒
        
        print(f"✅ 前端使用场景模拟成功")
        return True
        
    except Exception as e:
        print(f"❌ 前端模拟失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 GPU监控页面修复验证")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("• 修复了前端API调用的字段名问题 (data.status -> data.gpu_status)")
    print("• 增强了GPU状态API，添加了使用率、温度、功耗等信息")
    print("• 改进了错误处理和显示机制")
    print("• 添加了更详细的GPU监控数据")
    
    # 测试GPU状态API
    api_ok = test_gpu_status_api()
    
    # 测试API稳定性
    stability_ok = test_multiple_requests()
    
    # 模拟前端使用
    frontend_ok = simulate_frontend_usage()
    
    print(f"\n📋 修复验证结果")
    print("=" * 80)
    
    if api_ok and stability_ok and frontend_ok:
        print(f"🎉 GPU监控页面修复完全成功!")
        print(f"✅ GPU状态API正常工作")
        print(f"✅ API稳定性良好")
        print(f"✅ 前端集成正常")
        
        print(f"\n💡 修复成果:")
        print(f"• GPU使用率、内存使用、温度、功耗等信息正常显示")
        print(f"• 前端不再显示'错误'，而是显示真实的GPU数据")
        print(f"• API响应稳定，支持前端定期刷新")
        print(f"• 错误处理机制完善，提供有用的故障排除建议")
        
        print(f"\n🎯 用户体验改善:")
        print(f"• 可以实时监控GPU性能状态")
        print(f"• 了解GPU资源使用情况")
        print(f"• 及时发现GPU性能问题")
        print(f"• 优化深度学习训练资源分配")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步调整")
        print(f"GPU状态API: {'✅' if api_ok else '❌'}")
        print(f"API稳定性: {'✅' if stability_ok else '❌'}")
        print(f"前端集成: {'✅' if frontend_ok else '❌'}")
    
    print(f"\n🔧 使用说明")
    print("=" * 80)
    
    print(f"📊 GPU监控页面功能:")
    print(f"• 实时显示GPU使用率、内存使用、温度、功耗")
    print(f"• 提供GPU硬件和软件详细信息")
    print(f"• 支持自动刷新和手动刷新")
    print(f"• 历史数据图表显示")
    
    print(f"\n💡 监控建议:")
    print(f"• GPU使用率过高(>90%)时考虑优化模型")
    print(f"• 内存使用率过高(>90%)时减少批次大小")
    print(f"• 温度过高(>80°C)时检查散热")
    print(f"• 功耗异常时检查GPU状态")

if __name__ == '__main__':
    main()
