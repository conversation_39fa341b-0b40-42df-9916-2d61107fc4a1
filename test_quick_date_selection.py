#!/usr/bin/env python3
"""
测试快速日期选择功能
"""

from datetime import datetime, timedelta
import json

def test_date_calculations():
    """测试日期计算逻辑"""
    
    print("🧪 测试快速日期选择计算逻辑")
    print("=" * 50)
    
    # 模拟今天的日期
    today = datetime.now()
    
    # 测试不同的日期范围
    test_cases = [
        (1, "1天"),
        (3, "3天"),
        (7, "1周"),
        (30, "1月"),
        (60, "2月")
    ]
    
    print(f"📅 基准日期（今天）: {today.strftime('%Y-%m-%d')}")
    print()
    
    for days, description in test_cases:
        start_date = today - timedelta(days=days)
        end_date = today
        
        print(f"🔹 {description} ({days}天):")
        print(f"   开始日期: {start_date.strftime('%Y-%m-%d')}")
        print(f"   结束日期: {end_date.strftime('%Y-%m-%d')}")
        print(f"   实际天数: {(end_date - start_date).days} 天")
        print()

def generate_javascript_test():
    """生成JavaScript测试代码"""
    
    print("📝 生成JavaScript测试代码")
    print("=" * 50)
    
    js_code = """
// 测试快速日期选择功能
function testQuickDateSelection() {
    console.log('🧪 测试快速日期选择功能');
    
    const testCases = [
        {days: 1, description: '1天'},
        {days: 3, description: '3天'},
        {days: 7, description: '1周'},
        {days: 30, description: '1月'},
        {days: 60, description: '2月'}
    ];
    
    testCases.forEach(testCase => {
        console.log(`\\n🔹 测试 ${testCase.description}:`);
        
        // 模拟点击
        setQuickDateRange(testCase.days);
        
        // 获取设置后的日期
        const startDate = document.getElementById('backtestStartDate').value;
        const endDate = document.getElementById('backtestEndDate').value;
        
        console.log(`   开始日期: ${startDate}`);
        console.log(`   结束日期: ${endDate}`);
        
        // 验证日期差
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        
        console.log(`   实际天数: ${diffDays} 天`);
        console.log(`   预期天数: ${testCase.days} 天`);
        console.log(`   ✅ ${diffDays === testCase.days ? '通过' : '失败'}`);
    });
}

// 在浏览器控制台中运行此函数进行测试
// testQuickDateSelection();
"""
    
    print(js_code)
    
    # 保存到文件
    with open('quick_date_test.js', 'w', encoding='utf-8') as f:
        f.write(js_code)
    
    print("✅ JavaScript测试代码已保存到 quick_date_test.js")

def test_edge_cases():
    """测试边界情况"""
    
    print("🔍 测试边界情况")
    print("=" * 50)
    
    # 测试月末/月初的情况
    test_dates = [
        datetime(2024, 1, 31),  # 1月31日
        datetime(2024, 2, 29),  # 闰年2月29日
        datetime(2024, 3, 1),   # 3月1日
        datetime(2024, 12, 31), # 年末
    ]
    
    for test_date in test_dates:
        print(f"📅 测试基准日期: {test_date.strftime('%Y-%m-%d')}")
        
        for days in [1, 7, 30, 60]:
            start_date = test_date - timedelta(days=days)
            
            print(f"   {days}天前: {start_date.strftime('%Y-%m-%d')}")
        
        print()

def generate_html_demo():
    """生成HTML演示页面"""
    
    print("🌐 生成HTML演示页面")
    print("=" * 50)
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速日期选择测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>快速日期选择功能测试</h2>
        
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="backtestStartDate">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="backtestEndDate">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">
                            <i class="fas fa-clock me-2"></i>
                            快速时间选择
                        </label>
                        <div class="d-grid gap-2">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(1)">
                                    <i class="fas fa-calendar-day me-1"></i>1天
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(3)">
                                    <i class="fas fa-calendar-alt me-1"></i>3天
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(7)">
                                    <i class="fas fa-calendar-week me-1"></i>1周
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(30)">
                                    <i class="fas fa-calendar me-1"></i>1月
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="setQuickDateRange(60)">
                                    <i class="fas fa-calendar-plus me-1"></i>2月
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-success" onclick="testQuickDateSelection()">运行测试</button>
            </div>
        </div>
        
        <div class="mt-3">
            <div class="alert alert-info">
                <h6>测试说明：</h6>
                <ul class="mb-0">
                    <li>点击快速时间选择按钮，观察日期输入框的变化</li>
                    <li>点击"运行测试"按钮，在浏览器控制台查看测试结果</li>
                    <li>验证计算的日期范围是否正确</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 快速设置日期范围
        function setQuickDateRange(days) {
            const endDate = new Date();
            const startDate = new Date();
            
            startDate.setDate(endDate.getDate() - days);
            
            const formatDate = (date) => {
                return date.toISOString().split('T')[0];
            };
            
            document.getElementById('backtestEndDate').value = formatDate(endDate);
            document.getElementById('backtestStartDate').value = formatDate(startDate);
            
            const buttons = document.querySelectorAll('.btn-group .btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            const clickedButton = event.target.closest('button');
            if (clickedButton) {
                clickedButton.classList.add('active');
                setTimeout(() => {
                    clickedButton.classList.remove('active');
                }, 2000);
            }
            
            console.log(`📅 设置日期范围: ${formatDate(startDate)} 到 ${formatDate(endDate)} (${getDaysText(days)})`);
        }
        
        function getDaysText(days) {
            switch(days) {
                case 1: return '1天';
                case 3: return '3天';
                case 7: return '1周';
                case 30: return '1个月';
                case 60: return '2个月';
                default: return `${days}天`;
            }
        }
        
        function testQuickDateSelection() {
            console.log('🧪 开始测试快速日期选择功能');
            
            const testCases = [
                {days: 1, description: '1天'},
                {days: 3, description: '3天'},
                {days: 7, description: '1周'},
                {days: 30, description: '1月'},
                {days: 60, description: '2月'}
            ];
            
            testCases.forEach((testCase, index) => {
                setTimeout(() => {
                    console.log(`\\n🔹 测试 ${testCase.description}:`);
                    
                    setQuickDateRange(testCase.days);
                    
                    const startDate = document.getElementById('backtestStartDate').value;
                    const endDate = document.getElementById('backtestEndDate').value;
                    
                    console.log(`   开始日期: ${startDate}`);
                    console.log(`   结束日期: ${endDate}`);
                    
                    const start = new Date(startDate);
                    const end = new Date(endDate);
                    const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
                    
                    console.log(`   实际天数: ${diffDays} 天`);
                    console.log(`   预期天数: ${testCase.days} 天`);
                    console.log(`   ${diffDays === testCase.days ? '✅ 通过' : '❌ 失败'}`);
                }, index * 1000);
            });
        }
        
        // 页面加载时设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - 7); // 默认1周
            
            document.getElementById('backtestEndDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('backtestStartDate').value = startDate.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
"""
    
    with open('quick_date_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ HTML演示页面已保存到 quick_date_test.html")
    print("🌐 可以在浏览器中打开此文件进行测试")

def main():
    """主函数"""
    
    print("🧪 快速日期选择功能测试")
    print("=" * 60)
    
    # 测试日期计算
    test_date_calculations()
    
    # 测试边界情况
    test_edge_cases()
    
    # 生成JavaScript测试代码
    generate_javascript_test()
    
    # 生成HTML演示页面
    generate_html_demo()
    
    print("\n🎉 测试完成!")
    print("=" * 60)
    
    print("\n📋 功能特点:")
    print("✅ 支持1天、3天、1周、1月、2月快速选择")
    print("✅ 自动计算开始和结束日期")
    print("✅ 结束日期固定为今天")
    print("✅ 开始日期根据选择的天数自动计算")
    print("✅ 提供视觉反馈和成功提示")
    print("✅ 响应式设计，适配不同屏幕尺寸")

if __name__ == '__main__':
    main()
