#!/usr/bin/env python3
"""
测试最终优化后的参数组合
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.deep_learning_service import DeepLearningService

def test_final_optimization():
    """测试最终优化结果"""
    print("🎯 测试最终优化后的参数组合")
    print("=" * 50)
    
    dl_service = DeepLearningService()
    
    # 新的参数范围
    parameter_ranges = {
        'initial_balance': [10000],  # 1个值
        'lot_size': [0.01, 0.02],  # 2个值（减少）
        'stop_loss_pips': [30, 50, 80],  # 3个值
        'take_profit_pips': [60, 100, 150],  # 3个值
        'min_confidence': [0.10, 0.30, 0.50, 0.70],  # 4个值（减少）
        'cliff_brake_enabled': [False, True],  # 2个值
        'trailing_stop_enabled': [False, True],  # 2个值
        'trailing_stop_distance': [20, 30],  # 2个值
        'trailing_stop_step': [10, 15]  # 2个值
    }
    
    print("📊 参数范围:")
    total = 1
    for param, values in parameter_ranges.items():
        count = len(values)
        total *= count
        print(f"   {param}: {count}个值")
    
    print(f"\n🔢 理论总组合数: {total:,}")
    
    # 测试实际生成
    print("\n🧪 测试实际生成...")
    start_time = time.time()
    combinations = dl_service._generate_parameter_combinations()
    end_time = time.time()
    
    print(f"✅ 实际有效组合数: {len(combinations):,}")
    print(f"⏱️ 生成耗时: {end_time - start_time:.3f} 秒")
    
    # 估算优化时间
    estimated_time = len(combinations) * 0.5  # 每组合0.5秒
    print(f"\n⏱️ 估算优化时间: {estimated_time:.0f} 秒 ({estimated_time/60:.1f} 分钟)")
    
    if estimated_time < 300:
        print("✅ 优化时间合理 (< 5分钟)")
        time_status = "excellent"
    elif estimated_time < 600:
        print("⚠️ 优化时间较长 (5-10分钟)")
        time_status = "acceptable"
    else:
        print("❌ 优化时间过长 (> 10分钟)")
        time_status = "too_long"
    
    # 显示置信度值
    if combinations:
        confidence_values = sorted(set(combo['min_confidence'] for combo in combinations))
        print(f"\n🎯 置信度值: {[f'{c:.2f}' for c in confidence_values]}")
        print("✅ 置信度精度: 小数点后2位")
    
    # 显示优化对比
    original_combinations = 13500  # 原始组合数
    reduction = original_combinations - len(combinations)
    reduction_percent = reduction / original_combinations * 100
    
    print(f"\n📈 优化对比:")
    print(f"   原始组合数: {original_combinations:,}")
    print(f"   优化后组合数: {len(combinations):,}")
    print(f"   减少数量: {reduction:,}")
    print(f"   减少比例: {reduction_percent:.1f}%")
    
    # 显示示例组合
    if combinations:
        print(f"\n📋 参数组合示例 (前3个):")
        for i, combo in enumerate(combinations[:3]):
            print(f"   组合 {i+1}:")
            for key, value in combo.items():
                if key == 'min_confidence':
                    print(f"     {key}: {value:.2f}")
                else:
                    print(f"     {key}: {value}")
            print()
    
    return time_status, len(combinations)

def main():
    print("🔧 参数优化最终测试")
    print("=" * 60)
    print("🎯 目标: 置信度小数点后2位，优化时间 < 5分钟")
    print("=" * 60)
    
    time_status, combinations_count = test_final_optimization()
    
    print("\n" + "=" * 60)
    print("📋 最终优化结果")
    print("=" * 60)
    
    if time_status == "excellent":
        print("🎉 优化完美成功！")
        print("💡 优化成果:")
        print("   ✅ 置信度精度调整为小数点后2位")
        print("   ✅ 参数组合数量控制在合理范围")
        print("   ✅ 优化时间 < 5分钟，用户体验优秀")
        print("   ✅ 保持了参数覆盖的全面性")
        
        print(f"\n🚀 现在可以使用参数优化功能:")
        print(f"   • 组合数量: {combinations_count:,} 个")
        print(f"   • 置信度: 0.10, 0.30, 0.50, 0.70 (小数点后2位)")
        print(f"   • 预计完成时间: < 5分钟")
        print(f"   • 一次获取数据，多次回测比较")
        
    elif time_status == "acceptable":
        print("✅ 优化基本成功")
        print("💡 建议:")
        print("   • 可以考虑进一步减少参数范围")
        print("   • 或者添加进度提示改善用户体验")
        
    else:
        print("⚠️ 需要进一步优化")
        print("💡 建议:")
        print("   • 减少更多参数组合")
        print("   • 考虑分批次优化")

if __name__ == '__main__':
    main()
