#!/usr/bin/env python3
"""
测试推理API修复
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def get_available_model():
    """获取可用的模型"""
    session = login_session()
    if not session:
        return None
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') and result.get('models'):
                # 找到第一个完成训练的模型
                for model in result['models']:
                    if model['status'] == 'completed':
                        return model
        
        return None
        
    except Exception as e:
        print(f"❌ 获取模型失败: {e}")
        return None

def test_realtime_inference():
    """测试实时推理"""
    
    print("🔴 测试实时推理")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    # 获取可用模型
    model = get_available_model()
    if not model:
        print("❌ 没有可用的训练完成模型")
        return False
    
    print(f"✅ 使用模型: {model['name']} ({model['id'][:8]}...)")
    
    # 实时推理请求
    inference_data = {
        'model_id': model['id'],
        'symbol': 'XAUUSD',
        'timeframe': '1h',
        'inference_mode': 'realtime',
        'data_points': 50,
        'use_gpu': True,
        'show_confidence': True
        # 注意：实时推理不包含 start_date 和 end_date
    }
    
    try:
        print(f"📡 发送实时推理请求...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/inference',
            json=inference_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                
                if result.get('success'):
                    print(f"✅ 实时推理成功!")
                    
                    # 显示推理结果
                    inference_id = result.get('inference_id')
                    model_info = result.get('model_info', {})
                    inference_config = result.get('inference_config', {})
                    results = result.get('results', [])
                    metadata = result.get('metadata', {})
                    
                    print(f"📋 推理信息:")
                    print(f"   推理ID: {inference_id}")
                    print(f"   模型: {model_info.get('name')} ({model_info.get('type')})")
                    print(f"   品种: {model_info.get('symbol')}")
                    print(f"   时间框架: {model_info.get('timeframe')}")
                    
                    print(f"⚙️ 推理配置:")
                    print(f"   模式: {inference_config.get('mode')}")
                    print(f"   时间范围: {inference_config.get('time_range')}")
                    print(f"   数据点数: {inference_config.get('data_points')}")
                    print(f"   使用GPU: {inference_config.get('use_gpu')}")
                    
                    print(f"📊 推理结果:")
                    print(f"   预测数量: {len(results)}")
                    print(f"   数据样本: {metadata.get('data_samples')}")
                    print(f"   处理时间: {metadata.get('processing_time')}秒")
                    
                    # 显示前几个预测结果
                    if results:
                        print(f"🔮 预测详情 (前3个):")
                        for i, pred in enumerate(results[:3]):
                            print(f"   {i+1}. {pred.get('timestamp')[:19]}: {pred.get('prediction')} "
                                  f"(置信度: {pred.get('confidence')}, 目标价: {pred.get('price_target')})")
                    
                    return True
                    
                else:
                    print(f"❌ 推理失败: {result.get('error')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:200]}...")
                return False
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 实时推理测试失败: {e}")
        return False

def test_historical_inference():
    """测试历史推理"""
    
    print(f"\n🔵 测试历史推理")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    # 获取可用模型
    model = get_available_model()
    if not model:
        print("❌ 没有可用的训练完成模型")
        return False
    
    print(f"✅ 使用模型: {model['name']} ({model['id'][:8]}...)")
    
    # 历史推理请求
    inference_data = {
        'model_id': model['id'],
        'symbol': 'XAUUSD',
        'timeframe': '1h',
        'inference_mode': 'single',
        'data_points': 100,
        'start_date': '2024-07-01',
        'end_date': '2024-07-29',
        'use_gpu': True,
        'show_confidence': True
    }
    
    try:
        print(f"📊 发送历史推理请求...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/inference',
            json=inference_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                
                if result.get('success'):
                    print(f"✅ 历史推理成功!")
                    
                    # 显示推理结果
                    inference_config = result.get('inference_config', {})
                    results = result.get('results', [])
                    metadata = result.get('metadata', {})
                    
                    print(f"⚙️ 推理配置:")
                    print(f"   模式: {inference_config.get('mode')}")
                    print(f"   时间范围: {inference_config.get('time_range')}")
                    print(f"   数据点数: {inference_config.get('data_points')}")
                    
                    print(f"📊 推理结果:")
                    print(f"   预测数量: {len(results)}")
                    print(f"   数据样本: {metadata.get('data_samples')}")
                    print(f"   处理时间: {metadata.get('processing_time')}秒")
                    
                    return True
                    
                else:
                    print(f"❌ 推理失败: {result.get('error')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 历史推理测试失败: {e}")
        return False

def test_missing_time_range():
    """测试缺少时间范围的历史推理"""
    
    print(f"\n⚠️ 测试缺少时间范围的历史推理")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    model = get_available_model()
    if not model:
        print("❌ 没有可用的训练完成模型")
        return False
    
    # 历史推理请求但缺少时间范围
    inference_data = {
        'model_id': model['id'],
        'symbol': 'XAUUSD',
        'timeframe': '1h',
        'inference_mode': 'single',  # 历史推理模式
        'data_points': 100,
        # 故意不包含 start_date 和 end_date
        'use_gpu': True,
        'show_confidence': True
    }
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/inference',
            json=inference_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if not result.get('success'):
                expected_error = '历史推理模式需要指定时间范围'
                actual_error = result.get('error', '')
                
                if expected_error in actual_error:
                    print(f"✅ 正确检测到缺少时间范围: {actual_error}")
                    return True
                else:
                    print(f"⚠️ 错误信息不符合预期: {actual_error}")
                    return False
            else:
                print(f"❌ 应该失败但却成功了")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 推理API修复验证")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("• 添加了 /api/deep-learning/inference API路由")
    print("• 实现了深度学习推理服务方法")
    print("• 支持实时推理和历史推理模式")
    print("• 智能处理时间范围参数")
    print("• 返回结构化的推理结果")
    
    # 测试实时推理
    realtime_ok = test_realtime_inference()
    
    # 测试历史推理
    historical_ok = test_historical_inference()
    
    # 测试参数验证
    validation_ok = test_missing_time_range()
    
    print(f"\n📋 API修复验证结果")
    print("=" * 80)
    
    if realtime_ok and historical_ok and validation_ok:
        print(f"🎉 推理API修复完全成功!")
        print(f"✅ 实时推理功能正常")
        print(f"✅ 历史推理功能正常")
        print(f"✅ 参数验证正常")
        
        print(f"\n💡 修复成果:")
        print(f"• 解决了 'Unexpected token' JSON解析错误")
        print(f"• 推理API现在返回正确的JSON响应")
        print(f"• 支持实时和历史两种推理模式")
        print(f"• 提供详细的推理结果和元数据")
        print(f"• 智能处理不同模式的参数需求")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步调整")
        print(f"实时推理: {'✅' if realtime_ok else '❌'}")
        print(f"历史推理: {'✅' if historical_ok else '❌'}")
        print(f"参数验证: {'✅' if validation_ok else '❌'}")
    
    print(f"\n🎯 使用说明")
    print("=" * 80)
    
    print(f"📡 推理API端点: POST /api/deep-learning/inference")
    
    print(f"\n🔴 实时推理请求:")
    print(f"{{")
    print(f"  'model_id': '模型ID',")
    print(f"  'symbol': 'XAUUSD',")
    print(f"  'timeframe': '1h',")
    print(f"  'inference_mode': 'realtime',")
    print(f"  'data_points': 100,")
    print(f"  'use_gpu': true,")
    print(f"  'show_confidence': true")
    print(f"  // 注意：实时推理不需要时间范围")
    print(f"}}")
    
    print(f"\n🔵 历史推理请求:")
    print(f"{{")
    print(f"  'model_id': '模型ID',")
    print(f"  'symbol': 'XAUUSD',")
    print(f"  'timeframe': '1h',")
    print(f"  'inference_mode': 'single',")
    print(f"  'start_date': '2024-07-01',")
    print(f"  'end_date': '2024-07-29',")
    print(f"  'data_points': 100,")
    print(f"  'use_gpu': true,")
    print(f"  'show_confidence': true")
    print(f"}}")

if __name__ == '__main__':
    main()
