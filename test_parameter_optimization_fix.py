#!/usr/bin/env python3
"""
测试参数优化功能修复
"""

import requests
import json
import time

def test_parameter_optimization():
    """测试参数优化功能"""
    print("🧪 测试参数优化功能修复")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        'model_id': 'c9778e57-9a24-440f-9f31-36b9e47d3643',  # 使用日志中的模型ID
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'optimization_period': 'week'
    }
    
    print(f"📋 测试配置:")
    print(f"   模型ID: {test_data['model_id']}")
    print(f"   品种: {test_data['symbol']}")
    print(f"   时间框架: {test_data['timeframe']}")
    print(f"   优化周期: {test_data['optimization_period']}")
    
    try:
        print(f"\n🚀 发送参数优化请求...")
        
        # 发送请求
        response = requests.post(
            'http://localhost:5000/api/deep-learning/parameter-optimization',
            json=test_data,
            timeout=300  # 5分钟超时
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 参数优化成功!")
                print(f"📊 测试结果:")
                print(f"   总组合数: {result.get('total_combinations', 0)}")
                print(f"   成功组合数: {result.get('successful_combinations', 0)}")
                print(f"   优化周期: {result.get('optimization_period', 'unknown')}")
                
                # 显示最佳参数
                best_params = result.get('best_parameters')
                if best_params:
                    print(f"\n🏆 最佳参数组合:")
                    print(f"   手数: {best_params.get('lot_size', 'N/A')}")
                    print(f"   止损: {best_params.get('stop_loss_pips', 'N/A')} pips")
                    print(f"   止盈: {best_params.get('take_profit_pips', 'N/A')} pips")
                    print(f"   置信度: {best_params.get('min_confidence', 'N/A')}")
                    print(f"   悬崖勒马: {best_params.get('cliff_brake_enabled', 'N/A')}")
                
                # 显示前3名结果
                optimization_results = result.get('optimization_results', [])
                if optimization_results:
                    print(f"\n📈 前3名结果:")
                    for i, res in enumerate(optimization_results[:3], 1):
                        print(f"   {i}. 评分: {res.get('score', 0):.2f}, "
                              f"收益率: {res.get('total_return', 0):.2f}%, "
                              f"胜率: {res.get('win_rate', 0):.1f}%")
                
                return True
                
            else:
                print(f"❌ 参数优化失败: {result.get('error', '未知错误')}")
                return False
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('error', '未知错误')}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 (5分钟)")
        print("💡 参数优化可能需要更长时间，请检查后端日志")
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        print("💡 请确保应用程序正在运行: python app.py")
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_frontend_elements():
    """测试前端元素是否存在"""
    print(f"\n🌐 测试前端元素...")
    
    try:
        # 测试主页是否可访问
        response = requests.get('http://localhost:5000/', timeout=10)
        
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            
            # 检查关键元素
            content = response.text
            
            elements_to_check = [
                'inferenceStatusCard',
                'inferenceStatus',
                'parameterOptimizationBtn',
                'optimizationPeriod'
            ]
            
            missing_elements = []
            for element in elements_to_check:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"⚠️ 缺少前端元素: {missing_elements}")
            else:
                print("✅ 前端关键元素都存在")
                
            return len(missing_elements) == 0
            
        else:
            print(f"❌ 前端页面不可访问: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 参数优化功能修复测试")
    print("=" * 80)
    
    # 1. 测试前端元素
    frontend_ok = test_frontend_elements()
    
    # 2. 测试参数优化功能
    optimization_ok = test_parameter_optimization()
    
    # 总结
    print(f"\n📊 测试总结")
    print("=" * 60)
    print(f"前端元素: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    print(f"参数优化: {'✅ 通过' if optimization_ok else '❌ 失败'}")
    
    if frontend_ok and optimization_ok:
        print(f"\n🎉 所有测试通过！参数优化功能修复成功。")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关问题：")
        
        if not frontend_ok:
            print("   - 检查前端页面和JavaScript代码")
            print("   - 确保所有必需的HTML元素都存在")
            
        if not optimization_ok:
            print("   - 检查MT5连接状态")
            print("   - 检查模型是否存在")
            print("   - 查看后端日志获取详细错误信息")
    
    print(f"\n💡 如果问题仍然存在:")
    print("1. 重启应用程序: python app.py")
    print("2. 检查MT5连接状态")
    print("3. 确保模型ID正确")
    print("4. 查看浏览器开发者工具的错误信息")

if __name__ == '__main__':
    main()
