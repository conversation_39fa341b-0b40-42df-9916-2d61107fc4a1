#!/usr/bin/env python3
"""
测试优化后的参数回测功能
"""

import sys
import os
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def login_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_parameter_optimization():
    """测试参数优化功能"""
    print("🧪 测试优化后的参数回测功能")
    print("=" * 60)
    
    session = login_session()
    if not session:
        return False
    
    # 测试参数优化
    optimization_config = {
        'model_id': 'test_model',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'optimization_period': 'week'  # 使用一周数据
    }
    
    print("📊 参数优化配置:")
    for key, value in optimization_config.items():
        print(f"   {key}: {value}")
    
    try:
        print(f"\n🚀 启动参数优化...")
        start_time = time.time()
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/parameter-optimization',
            json=optimization_config,
            headers={'Content-Type': 'application/json'},
            timeout=300  # 5分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 参数优化耗时: {duration:.1f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"✅ 参数优化成功!")
                
                # 显示结果统计
                total_combinations = result.get('total_combinations', 0)
                successful_combinations = result.get('successful_combinations', 0)
                
                print(f"📊 优化结果统计:")
                print(f"   总参数组合数: {total_combinations}")
                print(f"   成功回测组合数: {successful_combinations}")
                print(f"   成功率: {successful_combinations/total_combinations*100:.1f}%")
                print(f"   平均每组合耗时: {duration/total_combinations:.2f} 秒")
                
                # 显示最佳参数
                best_params = result.get('best_parameters')
                if best_params:
                    print(f"\n🏆 最佳参数组合:")
                    for key, value in best_params.items():
                        print(f"   {key}: {value}")
                
                # 显示前3名结果
                optimization_results = result.get('optimization_results', [])
                if optimization_results:
                    print(f"\n🥇 前3名回测结果:")
                    for i, opt_result in enumerate(optimization_results[:3]):
                        print(f"   第{i+1}名:")
                        print(f"     收益率: {opt_result.get('total_return', 0):.2f}%")
                        print(f"     胜率: {opt_result.get('win_rate', 0):.2f}%")
                        print(f"     最大回撤: {opt_result.get('max_drawdown', 0):.2f}%")
                        print(f"     夏普比率: {opt_result.get('sharpe_ratio', 0):.2f}")
                        print(f"     交易次数: {opt_result.get('total_trades', 0)}")
                        print(f"     综合评分: {opt_result.get('score', 0):.2f}")
                
                return True
                
            else:
                print(f"❌ 参数优化失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试参数优化失败: {e}")
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n📈 性能对比测试")
    print("=" * 60)
    
    session = login_session()
    if not session:
        return
    
    # 测试小规模参数优化的性能
    print("🔍 测试小规模参数优化性能...")
    
    # 直接调用深度学习服务进行测试
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        # 测试参数优化
        start_time = time.time()
        
        result = dl_service.run_parameter_optimization(
            model_id='test_model',
            symbol='XAUUSD',
            timeframe='H1',
            optimization_period='week'
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.get('success'):
            total_combinations = result.get('total_combinations', 0)
            successful_combinations = result.get('successful_combinations', 0)
            
            print(f"✅ 优化完成!")
            print(f"⏱️ 总耗时: {duration:.1f} 秒")
            print(f"📊 总组合数: {total_combinations}")
            print(f"✅ 成功组合数: {successful_combinations}")
            print(f"⚡ 平均每组合: {duration/max(1, total_combinations):.2f} 秒")
            
            # 估算性能提升
            estimated_old_time = total_combinations * 2  # 假设每次获取数据需要2秒
            performance_improvement = (estimated_old_time - duration) / estimated_old_time * 100
            
            print(f"\n📈 性能提升估算:")
            print(f"   优化前预估耗时: {estimated_old_time:.1f} 秒")
            print(f"   优化后实际耗时: {duration:.1f} 秒")
            print(f"   性能提升: {performance_improvement:.1f}%")
            
        else:
            print(f"❌ 优化失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def main():
    print("🚀 优化后的参数回测功能测试")
    print("=" * 60)
    print("💡 优化要点:")
    print("   1. 一次获取历史数据，多次回测比较")
    print("   2. 避免重复的MT5数据请求")
    print("   3. 提高参数优化效率")
    print("=" * 60)
    
    # 测试1: 参数优化功能
    success = test_parameter_optimization()
    
    # 测试2: 性能对比
    test_performance_comparison()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    if success:
        print("✅ 优化后的参数回测功能正常工作")
        print("🎯 主要改进:")
        print("   • 一次数据获取，多次回测执行")
        print("   • 显著减少MT5数据请求次数")
        print("   • 提高参数优化整体效率")
        print("   • 减少网络延迟和数据传输开销")
    else:
        print("❌ 参数回测功能存在问题，需要进一步调试")

if __name__ == '__main__':
    main()
