#!/usr/bin/env python3
"""
直接验证置信度和价格格式修复效果
"""

import sys
sys.path.append('.')

def test_confidence_validation():
    """测试置信度验证函数"""
    
    print("🔧 测试置信度验证函数")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        service = DeepLearningService()
        
        # 测试各种置信度值
        test_cases = [
            (2.74, "274%置信度"),  # 原问题：超过100%
            (1.218, "121.8%置信度"),  # 测试中发现的问题
            (0.95, "95%置信度"),   # 边界值
            (1.0, "100%置信度"),   # 边界值
            (0.5, "50%置信度"),    # 正常值
            (-0.1, "负置信度"),    # 异常值
            (10.0, "1000%置信度"), # 极端值
        ]
        
        print("测试用例:")
        all_passed = True
        
        for raw_confidence, description in test_cases:
            validated = service._validate_confidence(raw_confidence)
            
            # 检查是否在合理范围内
            is_valid = 0 <= validated <= 0.95
            
            status = "✅通过" if is_valid else "❌失败"
            print(f"  {description:<15} -> {validated:.3f} ({validated*100:.1f}%) {status}")
            
            if not is_valid:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_price_formatting():
    """测试价格格式化函数"""
    
    print("\n🔧 测试价格格式化函数")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        service = DeepLearningService()
        
        # 测试各种价格和品种
        test_cases = [
            (3287.0177999999996, 'XAUUSD', 2, "黄金价格"),
            (1.23456789, 'EURUSD', 5, "欧美价格"),
            (110.123456, 'USDJPY', 3, "美日价格"),
            (0.987654321, 'AUDUSD', 5, "澳美价格"),
        ]
        
        print("测试用例:")
        all_passed = True
        
        for raw_price, symbol, expected_decimals, description in test_cases:
            formatted = service._format_price(raw_price, symbol)
            
            # 检查小数位数
            price_str = str(formatted)
            if '.' in price_str:
                actual_decimals = len(price_str.split('.')[1])
            else:
                actual_decimals = 0
            
            is_valid = actual_decimals <= expected_decimals
            
            status = "✅通过" if is_valid else "❌失败"
            print(f"  {description:<12} {raw_price:.10f} -> {formatted} ({actual_decimals}位小数) {status}")
            
            if not is_valid:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_intelligent_inference_mock():
    """模拟测试智能推理函数的置信度和价格处理"""
    
    print("\n🔧 模拟测试智能推理函数")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        service = DeepLearningService()
        
        # 模拟数据
        mock_data = [
            {
                'symbol': 'XAUUSD',
                'close': 3319.78,
                'timestamp': '2025-07-29T15:00:00'
            }
        ]
        
        mock_model = {
            'name': 'Test Model',
            'symbol': 'XAUUSD'
        }
        
        print("模拟推理测试:")
        
        # 直接调用智能推理函数
        results = service._intelligent_inference(mock_data, mock_model, True)
        
        if results and len(results) > 0:
            result = results[0]
            
            confidence = result.get('confidence', 0)
            price_target = result.get('price_target', 0)
            prediction = result.get('prediction', 'N/A')
            
            print(f"  预测结果: {prediction}")
            print(f"  置信度: {confidence:.3f} ({confidence*100:.1f}%)")
            print(f"  目标价格: {price_target}")
            
            # 验证置信度
            confidence_ok = 0 <= confidence <= 0.95
            
            # 验证价格格式
            price_str = str(price_target)
            if '.' in price_str:
                decimal_places = len(price_str.split('.')[1])
            else:
                decimal_places = 0
            
            price_ok = decimal_places <= 2  # XAUUSD应该≤2位小数
            
            print(f"  置信度检查: {'✅通过' if confidence_ok else '❌失败'}")
            print(f"  价格格式检查: {'✅通过' if price_ok else '❌失败'} ({decimal_places}位小数)")
            
            return confidence_ok and price_ok
        else:
            print("  ❌ 没有推理结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def show_fix_summary():
    """显示修复总结"""
    
    print("\n📋 置信度和价格格式修复总结")
    print("=" * 80)
    
    print("🔧 修复的问题:")
    print("1. ❌ 原问题: 置信度274.0% (超过100%)")
    print("   ✅ 修复: 添加_validate_confidence()函数，限制在0-95%")
    
    print("\n2. ❌ 原问题: 目标价格3287.0177999999996 (小数位过长)")
    print("   ✅ 修复: 添加_format_price()函数，根据品种调整精度")
    
    print("\n🛠️ 修复的代码位置:")
    print("• _intelligent_inference(): 智能推理中的置信度计算")
    print("• _load_and_run_pytorch_model(): PyTorch模型推理中的置信度")
    print("• 价格目标计算: 所有价格计算都使用格式化函数")
    
    print("\n📊 验证函数:")
    print("• _validate_confidence(): 确保置信度在0-95%范围")
    print("• _format_price(): 根据交易品种格式化价格精度")
    
    print("\n🎯 期望效果:")
    print("• 置信度: 0-95% (不再超过100%)")
    print("• XAUUSD价格: 保留2位小数")
    print("• JPY货币对: 保留3位小数")
    print("• 其他货币对: 保留5位小数")

def main():
    """主函数"""
    
    print("🔧 置信度和价格格式修复验证")
    print("=" * 80)
    
    # 显示修复总结
    show_fix_summary()
    
    # 执行各项测试
    confidence_ok = test_confidence_validation()
    price_ok = test_price_formatting()
    inference_ok = test_intelligent_inference_mock()
    
    print(f"\n📊 最终验证结果")
    print("=" * 80)
    
    all_tests_passed = confidence_ok and price_ok and inference_ok
    
    if all_tests_passed:
        print("🎉 所有修复验证通过!")
        print("✅ 置信度验证函数正常工作")
        print("✅ 价格格式化函数正常工作")
        print("✅ 智能推理函数修复生效")
        
        print(f"\n💡 修复效果:")
        print("• 置信度不再超过100% ✅")
        print("• 价格格式规范化 ✅")
        print("• 显示效果清晰易读 ✅")
        print("• 计算结果准确可靠 ✅")
        
        print(f"\n🎯 用户体验提升:")
        print("• 推理结果更加可信")
        print("• 价格显示更加专业")
        print("• 避免了混淆和误解")
        print("• 提高了系统可靠性")
        
    else:
        print("❌ 部分验证失败")
        print(f"• 置信度验证: {'✅通过' if confidence_ok else '❌失败'}")
        print(f"• 价格格式验证: {'✅通过' if price_ok else '❌失败'}")
        print(f"• 推理函数验证: {'✅通过' if inference_ok else '❌失败'}")
        
        print(f"\n🔧 需要进一步检查:")
        if not confidence_ok:
            print("• 置信度验证函数逻辑")
        if not price_ok:
            print("• 价格格式化函数逻辑")
        if not inference_ok:
            print("• 智能推理函数中的修复应用")

if __name__ == '__main__':
    main()
