#!/usr/bin/env python3
"""
测试模型推理功能
"""

import requests
import json
import time

def test_model_inference():
    """测试模型推理"""
    
    print("🧠 测试AI模型推理功能")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        if not result.get('success'):
            print("❌ 模型列表API返回失败")
            return False
        
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        print(f"总模型数: {len(models)}")
        print(f"完成训练的模型: {len(completed_models)}")
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        # 测试第一个模型的推理
        test_model = completed_models[0]
        model_id = test_model['id']
        
        print(f"\n🔍 测试模型推理: {test_model['name']} ({model_id[:8]}...)")
        print(f"模型信息: {test_model['symbol']} {test_model['timeframe']}")
        
        # 准备推理请求
        inference_data = {
            'model_id': model_id,
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True
        }
        
        print(f"\n🚀 执行推理...")
        print(f"请求参数: {json.dumps(inference_data, indent=2, ensure_ascii=False)}")
        
        start_time = time.time()
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=inference_data,
                               headers={'Content-Type': 'application/json'})
        end_time = time.time()
        
        print(f"推理耗时: {end_time - start_time:.2f}秒")
        
        if response.status_code != 200:
            print(f"❌ 推理请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
        
        result = response.json()
        
        if not result.get('success'):
            print(f"❌ 推理失败: {result.get('error')}")
            return False
        
        # 检查推理结果
        results = result.get('results', [])
        processing_time = result.get('processing_time', 0)
        model_used = result.get('model_used', 'N/A')
        gpu_used = result.get('gpu_used', False)
        
        print("✅ 推理成功完成!")
        print("=" * 50)
        print(f"📊 推理统计:")
        print(f"  处理时间: {processing_time:.3f}秒")
        print(f"  使用模型: {model_used}")
        print(f"  GPU加速: {'是' if gpu_used else '否'}")
        print(f"  预测结果数: {len(results)}")
        
        if results:
            print(f"\n🎯 推理结果:")
            for i, pred in enumerate(results[:3], 1):  # 显示前3个结果
                print(f"  {i}. 预测: {pred.get('prediction', 'N/A')}")
                print(f"     置信度: {pred.get('confidence', 0)*100:.1f}%")
                print(f"     当前价格: {pred.get('current_price', 0):.5f}")
                print(f"     目标价格: {pred.get('price_target', 0):.5f}")
                print(f"     信号强度: {pred.get('analysis', {}).get('signal_strength', 'N/A')}")
                print(f"     分析原因: {pred.get('analysis', {}).get('reason', 'N/A')}")
                if i < len(results):
                    print()
            
            if len(results) > 3:
                print(f"  ... 还有 {len(results) - 3} 个预测结果")
            
            # 验证结果格式
            first_result = results[0]
            required_fields = ['prediction', 'confidence', 'current_price', 'price_target', 'analysis']
            missing_fields = [field for field in required_fields if field not in first_result]
            
            if missing_fields:
                print(f"\n⚠️ 结果格式不完整，缺失字段: {missing_fields}")
                return False
            else:
                print(f"\n✅ 结果格式完整")
            
            return True
        else:
            print(f"\n⚠️ 推理成功但没有返回预测结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_historical_inference():
    """测试历史推理"""
    
    print("\n🔵 测试历史推理")
    print("=" * 50)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models = response.json()['models']
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        
        # 历史推理请求
        inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'single',
            'data_points': 100,
            'start_date': '2024-07-01',
            'end_date': '2024-07-29',
            'use_gpu': True,
            'show_confidence': True
        }
        
        print(f"🔄 执行历史推理...")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=inference_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                results = result.get('results', [])
                print(f"✅ 历史推理成功: {len(results)} 个结果")
                return True
            else:
                print(f"❌ 历史推理失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 历史推理请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 历史推理测试异常: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 AI模型推理功能测试")
    print("=" * 80)
    
    # 测试实时推理
    realtime_success = test_model_inference()
    
    # 测试历史推理
    historical_success = test_historical_inference()
    
    print(f"\n📊 测试结果")
    print("=" * 80)
    
    if realtime_success and historical_success:
        print("🎉 所有测试成功!")
        print("✅ 模型加载问题已解决")
        print("✅ 推理功能正常工作")
        print("✅ 结果格式正确")
        
        print(f"\n💡 功能说明:")
        print("• 模型现在可以正确加载状态字典")
        print("• 支持实时推理和历史推理")
        print("• 返回完整的预测结果和分析")
        print("• GPU加速正常工作")
        
    elif realtime_success:
        print("🎉 实时推理测试成功!")
        print("✅ 模型加载问题已解决")
        print("⚠️ 历史推理可能需要进一步检查")
        
    else:
        print("❌ 测试失败")
        print("⚠️ 模型推理仍有问题")
        
        print(f"\n🔧 故障排除建议:")
        print("• 检查模型文件是否存在")
        print("• 验证模型配置信息")
        print("• 确认PyTorch环境正常")
        print("• 查看服务器详细日志")

if __name__ == '__main__':
    main()
