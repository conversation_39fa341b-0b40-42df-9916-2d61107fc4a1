#!/usr/bin/env python3
"""
测试推理和自动交易修复
"""

import requests
import json

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_model_inference_fix():
    """测试模型推理修复"""
    
    print("🧠 测试模型推理修复")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 1. 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print(f"❌ 获取模型失败: {response.status_code}")
            return False
        
        models_result = response.json()
        if not models_result.get('success'):
            print(f"❌ 获取模型失败: {models_result.get('error')}")
            return False
        
        models = models_result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print(f"❌ 没有找到训练完成的模型")
            return False
        
        # 使用第一个完成的模型
        test_model = completed_models[0]
        print(f"   使用模型: {test_model['name']} ({test_model['symbol']}, {test_model['timeframe']})")
        
        # 2. 执行推理
        print("🔮 执行模型推理...")
        
        inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 50,
            'use_gpu': True,
            'show_confidence': True
        }
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/inference',
            json=inference_data
        )
        
        print(f"   API响应状态: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 推理请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:300]}...")
            return False
        
        result = response.json()
        
        print(f"   API响应结构检查:")
        print(f"   - success: {result.get('success')}")
        print(f"   - results字段存在: {'results' in result}")
        
        if result.get('success'):
            results = result.get('results', [])
            print(f"   - results数量: {len(results)}")
            
            if results:
                print(f"   ✅ 推理成功: 获得 {len(results)} 个预测结果")
                
                # 显示前几个结果的详细信息
                for i, res in enumerate(results[:3]):
                    print(f"   结果 {i+1}:")
                    print(f"     预测: {res.get('prediction')}")
                    print(f"     当前价格: {res.get('current_price')}")
                    print(f"     目标价格: {res.get('price_target')}")
                    print(f"     置信度: {res.get('confidence')}")
                    if 'analysis' in res:
                        analysis = res['analysis']
                        print(f"     分析: 价格变化{analysis.get('price_change')}%, 趋势{analysis.get('trend')}")
                
                return True
            else:
                print(f"   ❌ 推理成功但results为空")
                return False
        else:
            print(f"   ❌ 推理失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理失败: {e}")
        return False

def test_auto_trading_start():
    """测试自动交易启动修复"""
    
    print(f"\n🤖 测试自动交易启动修复")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 1. 检查MT5连接状态
        print("🔌 检查MT5连接状态...")
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        
        if response.status_code == 200:
            mt5_status = response.json()
            print(f"   MT5连接状态: {'已连接' if mt5_status.get('connected') else '未连接'}")
            
            if not mt5_status.get('connected'):
                print("   尝试自动连接MT5...")
                connect_response = session.post('http://127.0.0.1:5000/api/mt5/auto-connect')
                if connect_response.status_code == 200:
                    connect_result = connect_response.json()
                    if connect_result.get('success'):
                        print("   ✅ MT5自动连接成功")
                    else:
                        print(f"   ❌ MT5自动连接失败: {connect_result.get('error')}")
                        return False
                else:
                    print(f"   ❌ 自动连接请求失败: {connect_response.status_code}")
                    return False
        else:
            print(f"   ❌ 检查MT5状态失败: {response.status_code}")
            return False
        
        # 2. 获取可用模型
        print("📋 获取交易模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print(f"❌ 获取模型失败: {response.status_code}")
            return False
        
        models_result = response.json()
        if not models_result.get('success'):
            print(f"❌ 获取模型失败: {models_result.get('error')}")
            return False
        
        models = models_result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print(f"❌ 没有找到训练完成的模型")
            return False
        
        test_model = completed_models[0]
        print(f"   使用模型: {test_model['name']}")
        
        # 3. 测试自动交易启动API
        print("🚀 测试自动交易启动API...")
        
        trading_config = {
            'lot_size': 0.01,
            'max_positions': 3,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.75,
            'inference_interval': 300,
            'trading_start_time': '00:05',
            'trading_end_time': '23:55',
            'enable_trailing_stop': False,
            'enable_news_filter': True
        }
        
        auto_trading_data = {
            'model_id': test_model['id'],
            'trading_config': trading_config
        }
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/auto-trading/start',
            json=auto_trading_data
        )
        
        print(f"   API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}...")
            
            if result.get('success'):
                print(f"   ✅ 自动交易启动成功")
                
                # 立即停止自动交易（测试用）
                print("   停止自动交易（测试完成）...")
                stop_response = session.post('http://127.0.0.1:5000/api/deep-learning/auto-trading/stop')
                if stop_response.status_code == 200:
                    stop_result = stop_response.json()
                    if stop_result.get('success'):
                        print("   ✅ 自动交易已停止")
                    else:
                        print(f"   ⚠️ 停止自动交易失败: {stop_result.get('error')}")
                
                return True
            else:
                print(f"   ❌ 自动交易启动失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 自动交易启动请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:300]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试自动交易启动失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 推理和自动交易修复测试")
    print("=" * 80)
    
    print("📋 修复内容:")
    print("1. 修复推理结果显示问题")
    print("   • 修正前端结果处理逻辑")
    print("   • 确保推理结果正确传递和显示")
    
    print("2. 修复MT5连接检查问题")
    print("   • 修正is_connected()方法调用")
    print("   • 使用get_connection_status()方法")
    print("   • 修复自动交易启动时的连接检查")
    
    # 测试推理修复
    inference_ok = test_model_inference_fix()
    
    # 测试自动交易启动修复
    auto_trading_ok = test_auto_trading_start()
    
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    if inference_ok and auto_trading_ok:
        print(f"🎉 所有修复都成功!")
        print(f"✅ 模型推理功能正常")
        print(f"✅ 自动交易启动正常")
        
        print(f"\n💡 修复效果:")
        print(f"🧠 推理结果:")
        print(f"• 现在能正确显示推理结果")
        print(f"• 包含预测方向、价格、置信度")
        print(f"• 提供详细的技术分析信息")
        
        print(f"\n🤖 自动交易:")
        print(f"• MT5连接检查正常工作")
        print(f"• 自动交易可以正常启动")
        print(f"• 错误处理机制完善")
        
        print(f"\n🎯 使用建议:")
        print(f"• 访问推理页面执行推理测试")
        print(f"• 确保MT5连接后启动自动交易")
        print(f"• 观察推理结果的详细信息")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步检查")
        print(f"模型推理: {'✅' if inference_ok else '❌'}")
        print(f"自动交易: {'✅' if auto_trading_ok else '❌'}")
        
        print(f"\n🔧 故障排除:")
        if not inference_ok:
            print(f"• 检查推理API的实现")
            print(f"• 确认模型数据格式正确")
            print(f"• 验证前端结果处理逻辑")
        if not auto_trading_ok:
            print(f"• 确保MT5客户端运行正常")
            print(f"• 检查MT5服务连接状态")
            print(f"• 验证自动交易API实现")

if __name__ == '__main__':
    main()
