#!/usr/bin/env python3
"""
测试训练修复效果
"""

import requests
import json
import time
import threading
from datetime import datetime

def test_training_with_monitoring():
    """测试训练并监控进度"""
    
    print("🧪 测试深度学习训练修复效果")
    print("=" * 80)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 启动一个小规模的训练任务
        training_config = {
            'model_type': 'LSTM',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'data_source': 'mt5',
                'days': 30,  # 减少数据量
                'data_points': 1000  # 限制数据点
            },
            'model_config': {
                'input_size': 5,
                'hidden_size': 32,  # 减少隐藏层大小
                'num_layers': 1,    # 减少层数
                'dropout': 0.2
            },
            'training_config': {
                'epochs': 20,       # 减少训练轮次
                'batch_size': 16,   # 使用较小批次
                'learning_rate': 0.001,
                'early_stopping_patience': 5
            }
        }
        
        print("🚀 启动训练任务...")
        print(f"配置: {json.dumps(training_config, indent=2, ensure_ascii=False)}")
        
        # 启动训练
        response = session.post('http://127.0.0.1:5000/api/deep-learning/train', json=training_config)
        
        if response.status_code != 200:
            print(f"❌ 启动训练失败: HTTP {response.status_code}")
            return False
        
        result = response.json()
        if not result.get('success'):
            print(f"❌ 启动训练失败: {result.get('error')}")
            return False
        
        task_id = result.get('task_id')
        print(f"✅ 训练任务已启动: {task_id}")
        
        # 监控训练进度
        print(f"\n📊 开始监控训练进度...")
        
        start_time = time.time()
        last_progress = 0
        stuck_count = 0
        max_stuck_count = 6  # 最多允许6次无进展（3分钟）
        
        while True:
            try:
                # 获取训练进度
                progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                
                if progress_response.status_code == 200:
                    progress_data = progress_response.json()
                    
                    if progress_data.get('success'):
                        current_progress = progress_data.get('progress', 0)
                        status = progress_data.get('status', 'unknown')
                        current_epoch = progress_data.get('current_epoch', 0)
                        total_epochs = progress_data.get('total_epochs', 0)
                        train_loss = progress_data.get('train_loss', 0)
                        
                        elapsed_time = time.time() - start_time
                        
                        print(f"[{elapsed_time:.0f}s] 状态: {status} | 进度: {current_progress:.1f}% | 轮次: {current_epoch}/{total_epochs} | 损失: {train_loss:.4f}")
                        
                        # 检查进度是否有更新
                        if current_progress > last_progress:
                            last_progress = current_progress
                            stuck_count = 0  # 重置卡住计数
                        else:
                            stuck_count += 1
                        
                        # 检查是否卡住
                        if stuck_count >= max_stuck_count:
                            print(f"⚠️ 训练可能卡住 (进度{stuck_count*30}秒无更新)")
                            
                            # 尝试停止训练
                            stop_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/stop')
                            if stop_response.status_code == 200:
                                print(f"🛑 已发送停止信号")
                            
                            return False
                        
                        # 检查训练是否完成
                        if status in ['completed', 'failed', 'stopped']:
                            print(f"\n🎯 训练结束: {status}")
                            
                            if status == 'completed':
                                print(f"✅ 训练成功完成!")
                                print(f"最终进度: {current_progress:.1f}%")
                                print(f"总耗时: {elapsed_time:.0f}秒")
                                return True
                            else:
                                print(f"❌ 训练未成功完成: {status}")
                                return False
                    else:
                        print(f"❌ 获取进度失败: {progress_data.get('error')}")
                        return False
                else:
                    print(f"❌ 获取进度请求失败: HTTP {progress_response.status_code}")
                    return False
                
                # 等待30秒后再次检查
                time.sleep(30)
                
                # 超时检查（最多等待10分钟）
                if elapsed_time > 600:
                    print(f"⏰ 测试超时（10分钟），停止监控")
                    
                    # 尝试停止训练
                    stop_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/stop')
                    if stop_response.status_code == 200:
                        print(f"🛑 已发送停止信号")
                    
                    return False
                
            except Exception as e:
                print(f"❌ 监控异常: {e}")
                time.sleep(30)
                continue
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gpu_memory_management():
    """测试GPU内存管理"""
    
    print(f"\n🧹 测试GPU内存管理")
    print("=" * 60)
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            print("❌ CUDA不可用")
            return False
        
        device = torch.device('cuda')
        
        # 获取初始内存状态
        initial_allocated = torch.cuda.memory_allocated(0) / 1024**3
        initial_reserved = torch.cuda.memory_reserved(0) / 1024**3
        
        print(f"初始GPU内存: 已分配 {initial_allocated:.2f}GB, 已保留 {initial_reserved:.2f}GB")
        
        # 创建一些张量测试内存分配
        tensors = []
        for i in range(10):
            tensor = torch.randn(1000, 1000).to(device)
            tensors.append(tensor)
            
            allocated = torch.cuda.memory_allocated(0) / 1024**3
            reserved = torch.cuda.memory_reserved(0) / 1024**3
            
            print(f"创建张量 {i+1}: 已分配 {allocated:.2f}GB, 已保留 {reserved:.2f}GB")
        
        # 清理张量
        del tensors
        torch.cuda.empty_cache()
        
        # 获取清理后的内存状态
        final_allocated = torch.cuda.memory_allocated(0) / 1024**3
        final_reserved = torch.cuda.memory_reserved(0) / 1024**3
        
        print(f"清理后GPU内存: 已分配 {final_allocated:.2f}GB, 已保留 {final_reserved:.2f}GB")
        
        # 检查内存是否正确释放
        if final_allocated <= initial_allocated + 0.1:  # 允许0.1GB的误差
            print("✅ GPU内存管理正常")
            return True
        else:
            print("⚠️ GPU内存可能存在泄露")
            return False
    
    except Exception as e:
        print(f"❌ GPU内存测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🔧 深度学习训练修复效果测试")
    print("=" * 80)
    
    print("📋 测试项目:")
    print("1. GPU内存管理测试")
    print("2. 训练任务监控测试")
    print("3. 卡住检测和恢复测试")
    
    # 1. GPU内存管理测试
    gpu_test = test_gpu_memory_management()
    
    # 2. 训练任务测试
    training_test = test_training_with_monitoring()
    
    # 总结测试结果
    print(f"\n📊 测试结果总结")
    print("=" * 80)
    
    print(f"GPU内存管理: {'✅ 通过' if gpu_test else '❌ 失败'}")
    print(f"训练任务监控: {'✅ 通过' if training_test else '❌ 失败'}")
    
    if gpu_test and training_test:
        print(f"\n🎉 所有测试通过!")
        print(f"✅ 训练卡住问题已彻底解决")
        print(f"✅ GPU内存管理正常")
        print(f"✅ 训练监控机制有效")
        
        print(f"\n💡 使用建议:")
        print(f"• 现在可以正常进行深度学习训练")
        print(f"• 建议使用批次大小 16-32")
        print(f"• 建议训练轮次 20-100")
        print(f"• 启动训练监控脚本: python training_monitor.py")
        
    else:
        print(f"\n⚠️ 部分测试失败，可能需要进一步调试")
        
        if not gpu_test:
            print(f"• GPU内存管理需要检查")
        if not training_test:
            print(f"• 训练流程需要进一步优化")

if __name__ == '__main__':
    main()
