#!/usr/bin/env python3
"""
测试参数优化前端修复
"""

import sys
import os
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def login_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_parameter_optimization_api():
    """测试参数优化API"""
    print("🧪 测试参数优化API")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 确保MT5连接
    print("1. 确保MT5连接...")
    from services.mt5_service import mt5_service
    
    if not mt5_service.get_connection_status().get('connected', False):
        print("   MT5未连接，尝试连接...")
        success = mt5_service.connect()
        if not success:
            print("   ❌ MT5连接失败")
            return False
        print("   ✅ MT5连接成功")
    else:
        print("   ✅ MT5已连接")
    
    # 测试参数优化API
    print("2. 测试参数优化API...")
    optimization_config = {
        'model_id': 'test_model',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'optimization_period': 'week'
    }
    
    try:
        print(f"   发送请求: {optimization_config}")
        start_time = time.time()
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/parameter-optimization',
            json=optimization_config,
            headers={'Content-Type': 'application/json'},
            timeout=120  # 2分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   响应状态: {response.status_code}")
        print(f"   响应时间: {duration:.1f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("   ✅ API调用成功")
                
                # 检查返回数据结构
                required_fields = ['total_combinations', 'successful_combinations', 'optimization_results']
                missing_fields = []
                
                for field in required_fields:
                    if field not in result:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"   ⚠️ 缺少字段: {missing_fields}")
                else:
                    print("   ✅ 返回数据结构完整")
                
                # 显示结果摘要
                total = result.get('total_combinations', 0)
                successful = result.get('successful_combinations', 0)
                results = result.get('optimization_results', [])
                
                print(f"   总组合数: {total}")
                print(f"   成功组合数: {successful}")
                print(f"   返回结果数: {len(results)}")
                
                if results:
                    best_result = results[0]
                    print(f"   最佳收益率: {best_result.get('total_return', 0):.2f}%")
                    print(f"   最佳胜率: {best_result.get('win_rate', 0):.2f}%")
                
                return True
                
            else:
                print(f"   ❌ API返回失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
        return False

def test_frontend_elements():
    """测试前端元素是否存在"""
    print("\n🌐 测试前端元素")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5000/deep-learning/inference', timeout=10)
        
        if response.status_code == 200:
            print("✅ 模型推理页面可访问")
            
            content = response.text
            
            # 检查关键DOM元素
            elements_to_check = [
                ('inferenceStatusCard', '推理状态卡片'),
                ('inferenceStatus', '推理状态元素'),
                ('backtestCard', '回测卡片'),
                ('backtestStats', '回测统计'),
                ('backtestResults', '回测结果'),
                ('parameterOptimizationBtn', '参数优化按钮')
            ]
            
            missing_elements = []
            for element_id, description in elements_to_check:
                if f'id="{element_id}"' in content:
                    print(f"   ✅ {description} ({element_id})")
                else:
                    print(f"   ❌ {description} ({element_id}) - 未找到")
                    missing_elements.append(element_id)
            
            # 检查JavaScript函数
            js_functions = [
                ('showInferenceStatus', '显示推理状态函数'),
                ('displayOptimizationResults', '显示优化结果函数'),
                ('displayBacktestResults', '显示回测结果函数'),
                ('startParameterOptimization', '启动参数优化函数')
            ]
            
            for func_name, description in js_functions:
                if f'function {func_name}' in content:
                    print(f"   ✅ {description} ({func_name})")
                else:
                    print(f"   ❌ {description} ({func_name}) - 未找到")
            
            # 检查空值检查代码
            null_checks = [
                ('if (!statusCard || !statusElement)', '推理状态空值检查'),
                ('if (!backtestCard || !backtestStats || !backtestResults)', '回测结果空值检查')
            ]
            
            for check_code, description in null_checks:
                if check_code in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} - 未找到")
            
            return len(missing_elements) == 0
            
        else:
            print(f"❌ 页面不可访问: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 测试无效的参数优化请求
    print("1. 测试无效请求...")
    
    invalid_config = {
        'model_id': '',  # 空的模型ID
        'symbol': '',    # 空的品种
        'timeframe': '', # 空的时间框架
        'optimization_period': 'invalid'  # 无效的优化周期
    }
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/parameter-optimization',
            json=invalid_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 400:
            print("   ✅ 正确返回400错误")
            result = response.json()
            print(f"   错误信息: {result.get('error', '无')}")
            return True
        elif response.status_code == 200:
            result = response.json()
            if not result.get('success'):
                print("   ✅ API正确处理了无效请求")
                print(f"   错误信息: {result.get('error', '无')}")
                return True
            else:
                print("   ⚠️ API没有正确验证无效请求")
                return False
        else:
            print(f"   ⚠️ 意外的响应状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误处理测试失败: {e}")
        return False

def main():
    print("🔧 参数优化前端修复测试")
    print("=" * 60)
    print("💡 修复内容:")
    print("   1. 修复 displayOptimizationResults 中的 DOM 元素ID 不匹配")
    print("   2. 添加空值检查防止 'Cannot read properties of null' 错误")
    print("   3. 改进错误处理和用户反馈")
    print("=" * 60)
    
    # 测试1: 前端元素检查
    frontend_ok = test_frontend_elements()
    
    # 测试2: API功能测试
    api_ok = test_parameter_optimization_api()
    
    # 测试3: 错误处理测试
    error_handling_ok = test_error_handling()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    print(f"前端元素检查: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    print(f"API功能测试: {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"错误处理测试: {'✅ 通过' if error_handling_ok else '❌ 失败'}")
    
    if frontend_ok and api_ok and error_handling_ok:
        print("\n🎉 所有测试通过！参数优化功能已修复")
        print("💡 现在可以正常使用参数优化功能了")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        
        if not frontend_ok:
            print("   • 检查前端页面元素和JavaScript代码")
        if not api_ok:
            print("   • 检查后端API和MT5连接")
        if not error_handling_ok:
            print("   • 检查错误处理逻辑")

if __name__ == '__main__':
    main()
