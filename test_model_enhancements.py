#!/usr/bin/env python3
"""
测试模型管理和推理功能增强
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def test_enhanced_model_details():
    """测试增强的模型详情显示"""
    
    print("📊 测试增强的模型详情显示")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 获取模型列表
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') and result.get('models'):
                models = result['models']
                print(f"✅ 找到 {len(models)} 个模型")
                
                # 测试第一个模型的详情
                first_model = models[0]
                model_id = first_model['id']
                
                print(f"\n🔍 测试模型详情: {first_model['name']} ({model_id[:8]}...)")
                
                # 获取模型详情
                detail_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/models/{model_id}')
                
                if detail_response.status_code == 200:
                    detail_result = detail_response.json()
                    
                    if detail_result.get('success'):
                        model = detail_result['model']
                        
                        print(f"✅ 模型详情获取成功")
                        print(f"📋 增强的模型信息:")
                        
                        # 基本信息
                        print(f"   模型名称: {model.get('name')}")
                        print(f"   模型类型: {model.get('model_type')}")
                        print(f"   交易品种: {model.get('symbol')}")
                        print(f"   时间框架: {model.get('timeframe')}")
                        print(f"   状态: {model.get('status')}")
                        
                        # 新增的模型大小信息
                        model_size_mb = model.get('model_size_mb', 0)
                        print(f"   模型大小: {model_size_mb:.2f} MB")
                        
                        # 新增的训练数据信息
                        data_info = model.get('data_info', {})
                        print(f"   数据时间范围: {data_info.get('start_date')} 至 {data_info.get('end_date')}")
                        print(f"   总样本数: {data_info.get('total_samples', 0):,}")
                        print(f"   训练样本: {data_info.get('training_samples', 0):,}")
                        print(f"   验证样本: {data_info.get('validation_samples', 0):,}")
                        print(f"   使用特征: {data_info.get('features_used', [])}")
                        print(f"   数据质量: {data_info.get('data_quality', 'unknown')}")
                        
                        # 性能指标
                        performance = model.get('performance', {})
                        if performance:
                            print(f"   准确率: {performance.get('accuracy', 0) * 100:.2f}%")
                            print(f"   精确率: {performance.get('precision', 0) * 100:.2f}%")
                            print(f"   召回率: {performance.get('recall', 0) * 100:.2f}%")
                            print(f"   F1分数: {performance.get('f1_score', 0):.3f}")
                        
                        # 验证新增字段
                        required_fields = ['model_size_mb', 'data_info']
                        missing_fields = []
                        
                        for field in required_fields:
                            if field not in model:
                                missing_fields.append(field)
                        
                        if missing_fields:
                            print(f"⚠️ 缺少新增字段: {missing_fields}")
                            return False
                        else:
                            print(f"✅ 所有新增字段都存在")
                            return True
                            
                    else:
                        print(f"❌ 模型详情API错误: {detail_result.get('error')}")
                        return False
                else:
                    print(f"❌ 模型详情API请求失败: {detail_response.status_code}")
                    return False
                    
            else:
                print(f"❌ 没有找到模型或API错误: {result.get('error')}")
                return False
        else:
            print(f"❌ 模型列表API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试模型详情失败: {e}")
        return False

def test_inference_time_range():
    """测试推理时间范围配置"""
    
    print(f"\n🕐 测试推理时间范围配置")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 访问模型推理页面
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            html_content = response.text
            
            print(f"✅ 模型推理页面加载成功")
            
            # 检查时间范围相关的HTML元素
            time_range_elements = [
                'id="startDate"',
                'id="endDate"',
                'id="timeRangePreset"',
                '推理时间范围',
                'MT5真实市场数据',
                'applyTimeRangePreset()'
            ]
            
            missing_elements = []
            for element in time_range_elements:
                if element in html_content:
                    print(f"   ✅ 找到: {element}")
                else:
                    print(f"   ❌ 缺失: {element}")
                    missing_elements.append(element)
            
            if len(missing_elements) == 0:
                print(f"✅ 所有时间范围配置元素都存在")
                
                # 检查时间范围预设选项
                presets = ['last_week', 'last_month', 'last_3months', 'last_6months', 'last_year']
                preset_found = 0
                
                for preset in presets:
                    if preset in html_content:
                        preset_found += 1
                
                print(f"📊 时间范围预设: {preset_found}/{len(presets)} 个选项可用")
                
                if preset_found == len(presets):
                    print(f"✅ 所有时间范围预设都可用")
                    return True
                else:
                    print(f"⚠️ 部分时间范围预设缺失")
                    return False
                    
            else:
                print(f"❌ 缺少 {len(missing_elements)} 个时间范围配置元素")
                return False
                
        else:
            print(f"❌ 模型推理页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试推理时间范围失败: {e}")
        return False

def test_inference_api_parameters():
    """测试推理API参数"""
    
    print(f"\n🔧 测试推理API参数")
    print("=" * 60)
    
    print(f"📋 推理API应该支持的新参数:")
    print(f"• start_date: 推理开始日期")
    print(f"• end_date: 推理结束日期")
    print(f"• 基于指定时间范围的MT5真实历史数据进行推理")
    
    print(f"\n💡 前端JavaScript增强:")
    print(f"• applyTimeRangePreset(): 应用时间范围预设")
    print(f"• initializeDefaultTimeRange(): 初始化默认时间范围")
    print(f"• 推理请求包含start_date和end_date参数")
    
    return True

def main():
    """主函数"""
    
    print("🔧 模型管理和推理功能增强验证")
    print("=" * 80)
    
    print("📋 增强内容:")
    print("1. 模型详情增强:")
    print("   • 添加模型文件大小显示")
    print("   • 添加训练数据时间范围信息")
    print("   • 添加数据样本数量统计")
    print("   • 添加使用特征和数据质量信息")
    print("   • 改进性能指标显示")
    
    print("\n2. 推理功能增强:")
    print("   • 添加推理时间范围配置")
    print("   • 提供时间范围预设选项")
    print("   • 明确说明使用MT5真实历史数据")
    print("   • 更新推理API参数")
    
    # 测试模型详情增强
    model_details_ok = test_enhanced_model_details()
    
    # 测试推理时间范围
    inference_time_ok = test_inference_time_range()
    
    # 测试推理API参数
    api_params_ok = test_inference_api_parameters()
    
    print(f"\n📋 增强验证结果")
    print("=" * 80)
    
    if model_details_ok and inference_time_ok and api_params_ok:
        print(f"🎉 模型管理和推理功能增强完全成功!")
        print(f"✅ 模型详情显示增强成功")
        print(f"✅ 推理时间范围配置成功")
        print(f"✅ API参数更新成功")
        
        print(f"\n💡 增强成果:")
        print(f"• 模型详情更加丰富和直观")
        print(f"• 包含训练数据的完整信息")
        print(f"• 推理功能支持指定时间范围")
        print(f"• 明确使用MT5真实历史数据")
        print(f"• 提供便捷的时间范围预设")
        
        print(f"\n🎯 用户体验改善:")
        print(f"• 可以详细了解模型的训练情况")
        print(f"• 可以评估模型的数据质量")
        print(f"• 可以对特定时间段进行推理")
        print(f"• 推理结果更有针对性")
        
    else:
        print(f"⚠️ 部分功能可能需要进一步调整")
        print(f"模型详情增强: {'✅' if model_details_ok else '❌'}")
        print(f"推理时间范围: {'✅' if inference_time_ok else '❌'}")
        print(f"API参数更新: {'✅' if api_params_ok else '❌'}")
    
    print(f"\n🔧 使用说明")
    print("=" * 80)
    
    print(f"📊 模型详情页面:")
    print(f"• 点击模型列表中的'详情'按钮")
    print(f"• 查看增强的模型信息，包括:")
    print(f"  - 模型文件大小")
    print(f"  - 训练数据时间范围")
    print(f"  - 样本数量统计")
    print(f"  - 特征使用情况")
    print(f"  - 数据质量评估")
    
    print(f"\n🕐 推理时间范围配置:")
    print(f"• 在模型推理页面设置推理时间范围")
    print(f"• 可以手动选择开始和结束日期")
    print(f"• 可以使用预设时间范围:")
    print(f"  - 最近一周/一个月/三个月")
    print(f"  - 最近六个月/一年")
    print(f"• 推理将基于指定时间范围的MT5真实数据")

if __name__ == '__main__':
    main()
