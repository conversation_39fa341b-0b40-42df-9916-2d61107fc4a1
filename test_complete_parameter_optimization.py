#!/usr/bin/env python3
"""
测试完整的参数优化功能，确保所有参数都被包含
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.deep_learning_service import DeepLearningService

def test_complete_parameter_ranges():
    """测试完整的参数范围"""
    print("🔍 测试完整的参数范围")
    print("=" * 60)
    
    dl_service = DeepLearningService()
    
    # 生成参数组合
    print("1. 生成参数组合...")
    combinations = dl_service._generate_parameter_combinations()
    
    if not combinations:
        print("❌ 无法生成参数组合")
        return False
    
    print(f"✅ 成功生成 {len(combinations)} 个参数组合")
    
    # 检查第一个组合包含的所有参数
    first_combo = combinations[0]
    print(f"\n2. 检查参数完整性...")
    print(f"   第一个组合的参数:")
    
    expected_params = [
        'initial_balance',
        'lot_size', 
        'stop_loss_pips',
        'take_profit_pips',
        'min_confidence',
        'cliff_brake_enabled',
        'trailing_stop_enabled', 
        'trailing_stop_distance',
        'trailing_stop_step'
    ]
    
    missing_params = []
    for param in expected_params:
        if param in first_combo:
            value = first_combo[param]
            print(f"     ✅ {param}: {value}")
        else:
            print(f"     ❌ {param}: 缺失")
            missing_params.append(param)
    
    if missing_params:
        print(f"\n❌ 缺失参数: {missing_params}")
        return False
    else:
        print(f"\n✅ 所有参数都已包含")
    
    # 统计每个参数的取值范围
    print(f"\n3. 统计参数取值范围...")
    param_values = {}
    
    for combo in combinations:
        for param, value in combo.items():
            if param not in param_values:
                param_values[param] = set()
            param_values[param].add(value)
    
    for param, values in param_values.items():
        sorted_values = sorted(list(values))
        print(f"   {param}: {len(sorted_values)}个值 -> {sorted_values}")
    
    # 计算总组合数
    total_theoretical = 1
    for param, values in param_values.items():
        total_theoretical *= len(values)
    
    print(f"\n📊 组合统计:")
    print(f"   理论组合数: {total_theoretical:,}")
    print(f"   实际组合数: {len(combinations):,}")
    print(f"   过滤率: {(total_theoretical - len(combinations)) / total_theoretical * 100:.1f}%")
    
    return True

def test_parameter_display_format():
    """测试参数显示格式"""
    print("\n🎨 测试参数显示格式")
    print("=" * 60)
    
    dl_service = DeepLearningService()
    combinations = dl_service._generate_parameter_combinations()
    
    if not combinations:
        print("❌ 无法生成参数组合")
        return False
    
    print("📋 参数组合示例 (前5个):")
    for i, combo in enumerate(combinations[:5]):
        print(f"\n   组合 {i+1}:")
        print(f"     手数: {combo['lot_size']}")
        print(f"     止损: {combo['stop_loss_pips']} pips")
        print(f"     止盈: {combo['take_profit_pips']} pips")
        print(f"     置信度: {combo['min_confidence']:.2f}")
        print(f"     悬崖勒马: {'启用' if combo['cliff_brake_enabled'] else '禁用'}")
        print(f"     移动止损: {'启用' if combo['trailing_stop_enabled'] else '禁用'}")
        print(f"     移动止损距离: {combo['trailing_stop_distance']} pips")
        print(f"     移动止损步长: {combo['trailing_stop_step']} pips")
    
    return True

def test_optimization_result_structure():
    """测试优化结果结构"""
    print("\n📊 测试优化结果结构")
    print("=" * 60)
    
    # 模拟优化结果结构
    sample_result = {
        'rank': 1,
        'parameters': {
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 30,
            'take_profit_pips': 60,
            'min_confidence': 0.10,
            'cliff_brake_enabled': False,
            'trailing_stop_enabled': True,
            'trailing_stop_distance': 20,
            'trailing_stop_step': 10
        },
        'score': 30.5,
        'total_return': 0.86,
        'win_rate': 57.1,
        'max_drawdown': 2.3,
        'sharpe_ratio': 1.45,
        'total_trades': 15
    }
    
    print("✅ 优化结果应包含的字段:")
    print(f"   排名: {sample_result['rank']}")
    print(f"   评分: {sample_result['score']}")
    print(f"   收益率: {sample_result['total_return']:.2f}%")
    print(f"   胜率: {sample_result['win_rate']:.1f}%")
    print(f"   最大回撤: {sample_result['max_drawdown']:.1f}%")
    print(f"   夏普比率: {sample_result['sharpe_ratio']:.2f}")
    print(f"   交易次数: {sample_result['total_trades']}")
    
    print(f"\n📋 参数详情:")
    params = sample_result['parameters']
    for key, value in params.items():
        if key == 'min_confidence':
            print(f"   {key}: {value:.2f}")
        elif isinstance(value, bool):
            print(f"   {key}: {'启用' if value else '禁用'}")
        else:
            print(f"   {key}: {value}")
    
    return True

def calculate_new_combination_count():
    """计算新的组合数量"""
    print("\n🧮 计算新的组合数量")
    print("=" * 60)
    
    # 新的参数范围
    parameter_ranges = {
        'initial_balance': [10000],  # 1个值
        'lot_size': [0.01, 0.02],  # 2个值
        'stop_loss_pips': [30, 50, 80],  # 3个值
        'take_profit_pips': [60, 100, 150],  # 3个值
        'min_confidence': [0.10, 0.30, 0.50],  # 3个值
        'cliff_brake_enabled': [False, True],  # 2个值
        'trailing_stop_enabled': [False, True],  # 2个值
        'trailing_stop_distance': [20, 30],  # 2个值（恢复）
        'trailing_stop_step': [10, 15]  # 2个值（恢复）
    }
    
    print("📊 参数范围:")
    total = 1
    for param, values in parameter_ranges.items():
        count = len(values)
        total *= count
        print(f"   {param}: {count}个值 -> {values}")
    
    print(f"\n🔢 理论总组合数: {total:,}")
    
    # 估算有效组合数（考虑合理性检查）
    estimated_valid = int(total * 0.65)  # 估计65%的组合是有效的
    print(f"📈 估计有效组合数: {estimated_valid:,}")
    
    # 估算时间
    estimated_time = estimated_valid * 0.5 / 60  # 分钟
    print(f"⏱️ 估算优化时间: {estimated_time:.1f} 分钟")
    
    if estimated_time < 5:
        status = "✅ 优秀"
    elif estimated_time < 10:
        status = "⚠️ 可接受"
    else:
        status = "❌ 过长"
    print(f"   时间评价: {status}")
    
    return total, estimated_valid, estimated_time

def main():
    print("🔧 完整参数优化功能测试")
    print("=" * 80)
    print("🎯 目标: 确保所有交易参数都被包含在优化中")
    print("=" * 80)
    
    # 测试1: 参数范围完整性
    params_complete = test_complete_parameter_ranges()
    
    # 测试2: 参数显示格式
    display_ok = test_parameter_display_format()
    
    # 测试3: 优化结果结构
    structure_ok = test_optimization_result_structure()
    
    # 测试4: 计算组合数量
    total, valid, time_minutes = calculate_new_combination_count()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    
    print(f"参数完整性: {'✅ 通过' if params_complete else '❌ 失败'}")
    print(f"显示格式: {'✅ 正常' if display_ok else '❌ 异常'}")
    print(f"结果结构: {'✅ 完整' if structure_ok else '❌ 不完整'}")
    print(f"组合数量: {valid:,} 个")
    print(f"预计时间: {time_minutes:.1f} 分钟")
    
    if params_complete and display_ok and structure_ok:
        print("\n🎉 完整参数优化功能测试通过！")
        print("\n✅ 现在参数优化将包含所有交易参数:")
        print("   • 基础参数: 手数、止损、止盈、置信度")
        print("   • 高级功能: 悬崖勒马、移动止损")
        print("   • 移动止损配置: 触发距离、跟踪步长")
        print("   • 结果显示: 完整的参数组合和性能指标")
        
        if time_minutes < 10:
            print(f"\n⚡ 优化时间控制良好 ({time_minutes:.1f} 分钟)")
        else:
            print(f"\n⚠️ 优化时间较长 ({time_minutes:.1f} 分钟)，建议进一步优化")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == '__main__':
    main()
