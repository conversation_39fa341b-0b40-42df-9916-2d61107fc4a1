#!/usr/bin/env python3
"""
测试增强的深度学习训练进度显示
"""

import requests
import json
import time
import sqlite3
from datetime import datetime

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def cleanup_stuck_tasks():
    """清理卡住的任务"""
    
    print("🧹 清理卡住的训练任务")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 清理所有running状态的任务
        cursor.execute("""
            UPDATE training_tasks 
            SET status = 'failed', 
                completed_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP,
                logs = json_set(COALESCE(logs, '{}'), '$.error', '清理卡住的任务')
            WHERE status = 'running'
        """)
        
        affected = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"✅ 已清理 {affected} 个卡住的任务")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def test_gpu_status_api():
    """测试GPU状态API"""
    
    print(f"\n🎮 测试GPU状态API")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                gpu_data = result['gpu_status']
                print(f"✅ GPU状态API正常:")
                print(f"   GPU可用: {gpu_data.get('gpu_available')}")
                print(f"   设备: {gpu_data.get('device_name')}")
                print(f"   总内存: {gpu_data.get('total_memory_gb')}GB")
                print(f"   可用内存: {gpu_data.get('available_memory_gb')}GB")
                print(f"   使用率: {gpu_data.get('memory_usage_percent')}%")
                return True
            else:
                print(f"❌ GPU状态API错误: {result.get('error')}")
                return False
        else:
            print(f"❌ GPU状态API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ GPU状态测试失败: {e}")
        return False

def start_test_training():
    """启动测试训练"""
    
    print(f"\n🚀 启动测试训练")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return None
    
    # 使用简单配置
    config = {
        'model_name': f'enhanced_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 3,
        'batch_size': 8,
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 5,
        'features': ['close', 'volume']
    }
    
    print(f"📝 训练配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return None

def monitor_enhanced_progress(task_id, duration=180):
    """监控增强的训练进度"""
    
    print(f"\n📊 监控增强的训练进度 (任务: {task_id})")
    print("=" * 80)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    start_time = time.time()
    last_progress = -1
    last_stage = None
    progress_changes = 0
    stage_changes = 0
    data_info_collected = False
    
    print(f"🔄 开始监控 (时长: {duration}秒):")
    
    while time.time() - start_time < duration:
        try:
            elapsed = time.time() - start_time
            
            # 获取训练进度
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    current_status = progress_data.get('status', 'unknown')
                    current_progress = progress_data.get('progress', 0)
                    current_epoch = progress_data.get('epoch', 0)
                    total_epochs = progress_data.get('total_epochs', 0)
                    train_loss = progress_data.get('train_loss')
                    val_loss = progress_data.get('val_loss')
                    logs = progress_data.get('logs')
                    
                    # 解析阶段信息
                    current_stage = None
                    stage_message = None
                    stage_data = {}
                    
                    if logs:
                        try:
                            log_data = json.loads(logs)
                            current_stage = log_data.get('stage')
                            stage_message = log_data.get('message')
                            stage_data = log_data
                        except:
                            pass
                    
                    # 检查进度变化
                    if current_progress != last_progress:
                        progress_changes += 1
                        print(f"   [{elapsed:.1f}s] 📈 进度更新 #{progress_changes}: {current_progress}% (状态: {current_status})")
                        last_progress = current_progress
                    
                    # 检查阶段变化
                    if current_stage != last_stage and current_stage:
                        stage_changes += 1
                        print(f"   [{elapsed:.1f}s] 🔄 阶段变化 #{stage_changes}: {current_stage}")
                        if stage_message:
                            print(f"   [{elapsed:.1f}s] 💬 {stage_message}")
                        
                        # 显示阶段特定信息
                        if current_stage == 'data_fetched' and not data_info_collected:
                            data_points = stage_data.get('data_points', 0)
                            data_range = stage_data.get('data_range', {})
                            print(f"   [{elapsed:.1f}s] 📊 数据获取完成: {data_points}个数据点")
                            if data_range.get('start') and data_range.get('end'):
                                print(f"   [{elapsed:.1f}s] 📅 数据范围: {data_range['start']} 至 {data_range['end']}")
                            data_info_collected = True
                        
                        elif current_stage == 'data_ready':
                            train_samples = stage_data.get('train_samples', 0)
                            val_samples = stage_data.get('val_samples', 0)
                            feature_count = stage_data.get('feature_count', 0)
                            print(f"   [{elapsed:.1f}s] 🎯 数据准备完成:")
                            print(f"   [{elapsed:.1f}s]    训练样本: {train_samples}, 验证样本: {val_samples}")
                            print(f"   [{elapsed:.1f}s]    特征数量: {feature_count}")
                        
                        elif current_stage == 'model_training':
                            epochs = stage_data.get('epochs', 0)
                            batch_size = stage_data.get('batch_size', 0)
                            learning_rate = stage_data.get('learning_rate', 0)
                            print(f"   [{elapsed:.1f}s] 🚀 开始模型训练:")
                            print(f"   [{elapsed:.1f}s]    轮次: {epochs}, 批次大小: {batch_size}, 学习率: {learning_rate}")
                        
                        last_stage = current_stage
                    
                    # 显示训练信息
                    if current_epoch > 0 and train_loss is not None:
                        print(f"   [{elapsed:.1f}s] 🏋️ 训练中: 轮次{current_epoch}/{total_epochs}, 训练损失={train_loss:.4f}, 验证损失={val_loss:.4f if val_loss else 'N/A'}")
                    
                    # 检查训练状态
                    if current_status == 'completed':
                        print(f"   🎉 训练完成! (用时: {elapsed:.1f}秒)")
                        break
                    elif current_status == 'failed':
                        print(f"   ❌ 训练失败! (用时: {elapsed:.1f}秒)")
                        # 显示错误信息
                        if logs:
                            try:
                                log_data = json.loads(logs)
                                if 'error' in log_data:
                                    print(f"   错误信息: {log_data['error']}")
                            except:
                                pass
                        break
                
                else:
                    print(f"   ❌ API错误: {result.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
            
            time.sleep(2)  # 每2秒检查一次
            
        except Exception as e:
            print(f"   ❌ 监控异常: {e}")
            time.sleep(2)
    
    print(f"\n📊 监控总结:")
    print(f"   监控时长: {elapsed:.1f}秒")
    print(f"   进度更新次数: {progress_changes}")
    print(f"   阶段变化次数: {stage_changes}")
    print(f"   数据信息收集: {'✅' if data_info_collected else '❌'}")
    
    return progress_changes > 0 and stage_changes > 0

def main():
    """主函数"""
    
    print("🔧 增强的深度学习训练进度显示验证")
    print("=" * 80)
    
    # 清理卡住的任务
    cleanup_stuck_tasks()
    
    # 测试GPU状态API
    gpu_ok = test_gpu_status_api()
    
    if not gpu_ok:
        print("⚠️ GPU状态API有问题，但继续测试训练功能")
    
    # 启动测试训练
    task_id = start_test_training()
    
    if task_id:
        # 监控增强的进度
        success = monitor_enhanced_progress(task_id, duration=180)
        
        print(f"\n📋 增强功能验证结果")
        print("=" * 80)
        
        if success:
            print(f"🎉 增强的训练进度显示修复成功!")
            print(f"✅ 训练任务能够正常启动")
            print(f"✅ 进度更新机制正常工作")
            print(f"✅ 数据准备过程详细显示")
            print(f"✅ 阶段信息正确更新")
            print(f"✅ 数据获取信息完整显示")
            print(f"✅ 前端页面能显示详细信息")
            
            print(f"\n💡 用户现在可以看到:")
            print(f"• 📊 数据准备的每个阶段")
            print(f"• 📈 具体获取了多少数据点")
            print(f"• 📅 数据的时间范围")
            print(f"• 🎯 训练集和验证集的样本数量")
            print(f"• 🔢 特征数量和数据形状")
            print(f"• 🚀 模型训练的详细参数")
            print(f"• 🏋️ 实时的训练进度和损失值")
            
        else:
            print(f"⚠️ 增强功能仍有问题")
            print(f"✅ 基础功能已修复")
            print(f"❌ 详细信息显示可能不完整")
            
            print(f"\n🔧 建议进一步检查:")
            print(f"• 查看应用程序控制台日志")
            print(f"• 检查前端页面JavaScript控制台")
            print(f"• 确认数据库日志字段更新正常")
            
    else:
        print(f"\n❌ 无法启动测试训练")
        print(f"💡 建议:")
        print(f"• 检查应用程序状态")
        print(f"• 确认MT5连接正常")
        print(f"• 查看详细错误信息")

if __name__ == '__main__':
    main()
