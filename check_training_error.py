#!/usr/bin/env python3
"""
检查训练失败的具体错误
"""

import sqlite3
import json

def check_failed_training_error():
    """检查失败训练的错误信息"""
    
    print("🔍 检查最近失败训练的错误信息")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取最近失败的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, logs, created_at, completed_at
            FROM training_tasks 
            WHERE status = 'failed'
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        
        failed_tasks = cursor.fetchall()
        
        if not failed_tasks:
            print("❌ 没有找到失败的训练任务")
            conn.close()
            return
        
        print(f"📊 找到 {len(failed_tasks)} 个失败的训练任务:")
        
        for i, task in enumerate(failed_tasks, 1):
            task_id, model_id, status, progress, logs, created_at, completed_at = task
            
            print(f"\n🔹 失败任务 {i}: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   进度: {progress}%")
            print(f"   创建时间: {created_at}")
            print(f"   完成时间: {completed_at}")
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    print(f"   📝 错误日志:")
                    
                    if 'error' in log_data:
                        error_msg = log_data['error']
                        print(f"      ❌ 错误: {error_msg}")
                        
                        # 分析常见错误类型
                        if 'CUDA' in error_msg or 'GPU' in error_msg:
                            print(f"      💡 这是GPU相关错误")
                        elif 'memory' in error_msg.lower() or 'out of memory' in error_msg.lower():
                            print(f"      💡 这是内存不足错误")
                        elif 'data' in error_msg.lower() or 'shape' in error_msg.lower():
                            print(f"      💡 这是数据格式错误")
                        elif 'module' in error_msg.lower() or 'import' in error_msg.lower():
                            print(f"      💡 这是模块导入错误")
                        else:
                            print(f"      💡 其他类型错误")
                    
                    # 显示其他日志信息
                    for key, value in log_data.items():
                        if key != 'error':
                            print(f"      📊 {key}: {value}")
                            
                except json.JSONDecodeError:
                    print(f"   📝 原始日志: {logs}")
                except Exception as e:
                    print(f"   ❌ 日志解析失败: {e}")
            else:
                print(f"   📝 没有错误日志")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def suggest_solutions():
    """根据错误类型建议解决方案"""
    
    print(f"\n💡 常见问题解决方案")
    print("=" * 60)
    
    solutions = {
        "GPU/CUDA错误": [
            "检查CUDA版本与PyTorch版本兼容性",
            "尝试使用CPU模式训练",
            "重启应用程序重新初始化GPU",
            "检查GPU驱动程序是否最新"
        ],
        "内存不足": [
            "减少batch_size (如改为8或16)",
            "减少sequence_length (如改为5或10)",
            "减少模型复杂度",
            "关闭其他占用GPU内存的程序"
        ],
        "数据格式错误": [
            "检查历史数据是否充足",
            "确认MT5连接正常",
            "检查特征列是否存在",
            "验证数据预处理逻辑"
        ],
        "模块导入错误": [
            "检查PyTorch是否正确安装",
            "确认所有依赖包版本兼容",
            "重新安装相关Python包",
            "检查Python环境配置"
        ]
    }
    
    for error_type, solutions_list in solutions.items():
        print(f"\n🔧 {error_type}:")
        for solution in solutions_list:
            print(f"   • {solution}")

def create_simple_test_config():
    """创建一个最简单的测试配置"""
    
    print(f"\n🧪 建议的简单测试配置")
    print("=" * 60)
    
    simple_config = {
        'model_name': 'simple_test',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 1,           # 只训练1轮
        'batch_size': 8,       # 很小的批次
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 5,  # 很短的序列
        'features': ['close']  # 只使用收盘价
    }
    
    print(f"📝 最简配置 (用于测试):")
    for key, value in simple_config.items():
        print(f"   {key}: {value}")
    
    print(f"\n💡 使用方法:")
    print(f"1. 在模型训练页面使用以上配置")
    print(f"2. 如果这个配置能成功，逐步增加复杂度")
    print(f"3. 先增加epochs，再增加batch_size，最后增加sequence_length")
    print(f"4. 成功后可以添加更多特征 ['close', 'volume', 'high', 'low']")

def main():
    """主函数"""
    
    print("🔧 深度学习训练错误分析")
    print("=" * 80)
    
    # 检查失败任务的错误
    check_failed_training_error()
    
    # 建议解决方案
    suggest_solutions()
    
    # 提供简单测试配置
    create_simple_test_config()
    
    print(f"\n🎯 总结")
    print("=" * 80)
    
    print(f"✅ 好消息:")
    print(f"• task_id 未定义的错误已修复")
    print(f"• 训练任务能够正常启动")
    print(f"• 进度更新机制正常工作")
    
    print(f"\n⚠️ 当前问题:")
    print(f"• 训练在数据准备或模型初始化阶段失败")
    print(f"• 需要根据具体错误信息进行针对性修复")
    
    print(f"\n🚀 下一步:")
    print(f"1. 查看上面的错误信息，确定具体问题类型")
    print(f"2. 使用建议的简单配置进行测试")
    print(f"3. 如果简单配置成功，逐步增加复杂度")
    print(f"4. 如果仍然失败，可能需要检查环境配置")

if __name__ == '__main__':
    main()
