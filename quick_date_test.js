
// 测试快速日期选择功能
function testQuickDateSelection() {
    console.log('🧪 测试快速日期选择功能');
    
    const testCases = [
        {days: 1, description: '1天'},
        {days: 3, description: '3天'},
        {days: 7, description: '1周'},
        {days: 30, description: '1月'},
        {days: 60, description: '2月'}
    ];
    
    testCases.forEach(testCase => {
        console.log(`\n🔹 测试 ${testCase.description}:`);
        
        // 模拟点击
        setQuickDateRange(testCase.days);
        
        // 获取设置后的日期
        const startDate = document.getElementById('backtestStartDate').value;
        const endDate = document.getElementById('backtestEndDate').value;
        
        console.log(`   开始日期: ${startDate}`);
        console.log(`   结束日期: ${endDate}`);
        
        // 验证日期差
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        
        console.log(`   实际天数: ${diffDays} 天`);
        console.log(`   预期天数: ${testCase.days} 天`);
        console.log(`   ✅ ${diffDays === testCase.days ? '通过' : '失败'}`);
    });
}

// 在浏览器控制台中运行此函数进行测试
// testQuickDateSelection();
